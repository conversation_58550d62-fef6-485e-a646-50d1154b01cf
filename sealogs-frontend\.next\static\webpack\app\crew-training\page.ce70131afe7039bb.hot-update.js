"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/lib/crew-training-utils.ts":
/*!********************************************!*\
  !*** ./src/app/lib/crew-training-utils.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Transforming completed training data:\", {\n        totalRecords: trainingList.length,\n        sampleRecord: trainingList[0]\n    });\n    return trainingList.map((training)=>{\n        var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n        // Enhanced vessel data transformation with position information\n        let completeVesselData = training.vessel || {\n            id: 0,\n            title: \"Unknown\"\n        };\n        if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n            try {\n                // Get complete vessel data including position, icon, and other metadata\n                completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                // Ensure we preserve original vessel data if transformation fails\n                if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                    completeVesselData = training.vessel;\n                }\n                // Add position information if available\n                if (training.vessel.position && !completeVesselData.position) {\n                    completeVesselData.position = training.vessel.position;\n                }\n                // Add location type if available\n                if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                    completeVesselData.trainingLocationType = training.trainingLocationType;\n                }\n            } catch (error) {\n                console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                completeVesselData = training.vessel;\n            }\n        }\n        // Enhanced member deduplication and normalization\n        const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n        const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n            // Check if member already exists in the accumulator\n            const existingMember = acc.find((m)=>m.id === member.id);\n            if (existingMember) {\n                // Update existing member with more complete data\n                existingMember.firstName = member.firstName || existingMember.firstName;\n                existingMember.surname = member.surname || existingMember.surname;\n                existingMember.email = member.email || existingMember.email;\n            } else {\n                // Add new member with normalized data\n                acc.push({\n                    id: member.id,\n                    firstName: member.firstName || \"\",\n                    surname: member.surname || \"\",\n                    email: member.email || \"\",\n                    ...member // Preserve any additional member data\n                });\n            }\n            return acc;\n        }, []);\n        return {\n            id: training.id,\n            dueDate: training.date,\n            vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n            vessel: completeVesselData,\n            trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n            trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: deduplicatedMembers,\n            status: {\n                label: \"Completed\",\n                isOverdue: false,\n                class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                dueWithinSevenDays: false\n            },\n            category: \"completed\",\n            originalData: training\n        };\n    });\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Raw training session dues input:\", {\n        totalRecords: trainingSessionDues.length,\n        sampleRecord: trainingSessionDues[0]\n    });\n    // Apply the same filtering logic as CrewTrainingList\n    // Filter out crew members who are no longer assigned to the vessel\n    const filteredData = trainingSessionDues.filter((item)=>{\n        var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n        return (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n            return m.id === item.memberID;\n        });\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n        originalCount: trainingSessionDues.length,\n        filteredCount: filteredData.length,\n        removedCount: trainingSessionDues.length - filteredData.length\n    });\n    // Add status to each record\n    const dueWithStatus = filteredData.map((due)=>{\n        const status = (0,_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n        return {\n            ...due,\n            status\n        };\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n        totalRecords: dueWithStatus.length,\n        statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n            const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n            acc[key] = (acc[key] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    // Apply the same grouping logic as CrewTrainingList\n    // Group by vessel-trainingType-dueDate\n    const groupedDues = dueWithStatus.reduce((acc, due)=>{\n        const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n        if (!acc[key]) {\n            var _due_trainingSession;\n            acc[key] = {\n                id: due.id,\n                vesselID: due.vesselID,\n                vessel: due.vessel,\n                trainingTypeID: due.trainingTypeID,\n                trainingType: due.trainingType,\n                dueDate: due.dueDate,\n                status: due.status,\n                trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                members: []\n            };\n        }\n        acc[key].members.push(due.member);\n        return acc;\n    }, {});\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n        groupCount: Object.keys(groupedDues).length,\n        groupKeys: Object.keys(groupedDues)\n    });\n    // Merge members within each group (same as CrewTrainingList)\n    const mergedDues = Object.values(groupedDues).map((group)=>{\n        const mergedMembers = group.members.reduce((acc, member)=>{\n            const existingMember = acc.find((m)=>m.id === member.id);\n            if (existingMember) {\n                // Update existing member with more complete data\n                existingMember.firstName = member.firstName || existingMember.firstName;\n                existingMember.surname = member.surname || existingMember.surname;\n                existingMember.email = member.email || existingMember.email;\n            } else {\n                // Add new member with normalized data\n                acc.push({\n                    id: member.id,\n                    firstName: member.firstName || \"\",\n                    surname: member.surname || \"\",\n                    email: member.email || \"\",\n                    ...member // Preserve any additional member data\n                });\n            }\n            return acc;\n        }, []);\n        // Determine category based on status\n        let category;\n        if (group.status.isOverdue) {\n            category = \"overdue\";\n        } else if (group.status.dueWithinSevenDays) {\n            category = \"upcoming\";\n        } else {\n            category = \"upcoming\" // Default for future due dates\n            ;\n        }\n        // Enhanced vessel data with position information\n        const enhancedVessel = group.vessel || {\n            id: 0,\n            title: \"Unknown\"\n        };\n        // Add training location type if available\n        if (group.trainingLocationType && !enhancedVessel.trainingLocationType) {\n            enhancedVessel.trainingLocationType = group.trainingLocationType;\n        }\n        return {\n            id: group.id,\n            dueDate: group.dueDate,\n            vesselID: group.vesselID,\n            vessel: enhancedVessel,\n            trainingTypeID: group.trainingTypeID,\n            trainingType: group.trainingType || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: mergedMembers,\n            status: group.status,\n            category,\n            originalData: group\n        };\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n        totalRecords: mergedDues.length,\n        categoryBreakdown: mergedDues.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {}),\n        sampleRecord: mergedDues[0]\n    });\n    return mergedDues;\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID\n * @param data - Array of unified training data\n * @returns Deduplicated array\n */ const deduplicateTrainingData = (data)=>{\n    const seen = new Set();\n    return data.filter((item)=>{\n        if (seen.has(item.id)) {\n            return false;\n        }\n        seen.add(item.id);\n        return true;\n    });\n};\n/**\n * Sort unified training data with priority-based ordering\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    return data.sort((a, b)=>{\n        // First sort by priority (overdue > upcoming > completed)\n        const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n        if (priorityDiff !== 0) {\n            return priorityDiff;\n        }\n        // Within same priority, sort by date\n        const dateA = new Date(a.dueDate).getTime();\n        const dateB = new Date(b.dueDate).getTime();\n        if (a.category === \"overdue\") {\n            // For overdue: most overdue first (earliest due date first)\n            return dateA - dateB;\n        } else if (a.category === \"upcoming\") {\n            // For upcoming: soonest due date first\n            return dateA - dateB;\n        } else {\n            // For completed: most recent completion first (latest date first)\n            return dateB - dateA;\n        }\n    });\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    try {\n        // Debug logging removed for performance\n        // Transform overdue/upcoming training data\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        // Transform completed training data if requested\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        // Debug logging removed for performance\n        // Remove duplicates and sort with priority-based ordering\n        const deduplicatedData = deduplicateTrainingData(combinedData);\n        const sortedData = sortUnifiedTrainingData(deduplicatedData);\n        // Debug logging removed for performance\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGliL2NyZXctdHJhaW5pbmctdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFvRDs7VUFxQ3hDQzs7OztHQUFBQSxxQkFBQUE7QUFNWjs7Ozs7Q0FLQyxHQUNNLE1BQU1DLDRDQUE0QyxDQUNyREMsY0FDQUM7SUFFQSxJQUFJLENBQUNELGdCQUFnQixDQUFDRSxNQUFNQyxPQUFPLENBQUNILGVBQWU7UUFDL0MsT0FBTyxFQUFFO0lBQ2I7SUFFQUksUUFBUUMsR0FBRyxDQUFDLDRFQUFrRTtRQUMxRUMsY0FBY04sYUFBYU8sTUFBTTtRQUNqQ0MsY0FBY1IsWUFBWSxDQUFDLEVBQUU7SUFDakM7SUFFQSxPQUFPQSxhQUFhUyxHQUFHLENBQUMsQ0FBQ0M7WUFJSUEsa0JBMEJOQSxtQkEwQkxBLG1CQUVNQSxnQ0FBQUEsK0JBQUFBLHlCQUNGQSxnQ0FBQUE7UUExRGxCLGdFQUFnRTtRQUNoRSxJQUFJQyxxQkFBcUJELFNBQVNFLE1BQU0sSUFBSTtZQUFFQyxJQUFJO1lBQUdDLE9BQU87UUFBVTtRQUV0RSxJQUFJYix1QkFBcUJTLG1CQUFBQSxTQUFTRSxNQUFNLGNBQWZGLHVDQUFBQSxpQkFBaUJHLEVBQUUsR0FBRTtZQUMxQyxJQUFJO2dCQUNBLHdFQUF3RTtnQkFDeEVGLHFCQUFxQlYsa0JBQWtCUyxTQUFTRSxNQUFNLENBQUNDLEVBQUUsRUFBRUgsU0FBU0UsTUFBTTtnQkFFMUUsa0VBQWtFO2dCQUNsRSxJQUFJLENBQUNELHNCQUFzQixPQUFPQSx1QkFBdUIsVUFBVTtvQkFDL0RBLHFCQUFxQkQsU0FBU0UsTUFBTTtnQkFDeEM7Z0JBRUEsd0NBQXdDO2dCQUN4QyxJQUFJRixTQUFTRSxNQUFNLENBQUNHLFFBQVEsSUFBSSxDQUFDSixtQkFBbUJJLFFBQVEsRUFBRTtvQkFDMURKLG1CQUFtQkksUUFBUSxHQUFHTCxTQUFTRSxNQUFNLENBQUNHLFFBQVE7Z0JBQzFEO2dCQUVBLGlDQUFpQztnQkFDakMsSUFBSUwsU0FBU00sb0JBQW9CLElBQUksQ0FBQ0wsbUJBQW1CSyxvQkFBb0IsRUFBRTtvQkFDM0VMLG1CQUFtQkssb0JBQW9CLEdBQUdOLFNBQVNNLG9CQUFvQjtnQkFDM0U7WUFDSixFQUFFLE9BQU9DLE9BQU87Z0JBQ1piLFFBQVFjLElBQUksQ0FBQywrQ0FBK0NSLFNBQVNHLEVBQUUsRUFBRUk7Z0JBQ3pFTixxQkFBcUJELFNBQVNFLE1BQU07WUFDeEM7UUFDSjtRQUVBLGtEQUFrRDtRQUNsRCxNQUFNTyxhQUFhVCxFQUFBQSxvQkFBQUEsU0FBU1UsT0FBTyxjQUFoQlYsd0NBQUFBLGtCQUFrQlcsS0FBSyxLQUFJLEVBQUU7UUFDaEQsTUFBTUMsc0JBQXNCSCxXQUFXSSxNQUFNLENBQUMsQ0FBQ0MsS0FBWUM7WUFDdkQsb0RBQW9EO1lBQ3BELE1BQU1DLGlCQUFpQkYsSUFBSUcsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFZixFQUFFLEtBQUtZLE9BQU9aLEVBQUU7WUFFdkQsSUFBSWEsZ0JBQWdCO2dCQUNoQixpREFBaUQ7Z0JBQ2pEQSxlQUFlRyxTQUFTLEdBQUdKLE9BQU9JLFNBQVMsSUFBSUgsZUFBZUcsU0FBUztnQkFDdkVILGVBQWVJLE9BQU8sR0FBR0wsT0FBT0ssT0FBTyxJQUFJSixlQUFlSSxPQUFPO2dCQUNqRUosZUFBZUssS0FBSyxHQUFHTixPQUFPTSxLQUFLLElBQUlMLGVBQWVLLEtBQUs7WUFDL0QsT0FBTztnQkFDSCxzQ0FBc0M7Z0JBQ3RDUCxJQUFJUSxJQUFJLENBQUM7b0JBQ0xuQixJQUFJWSxPQUFPWixFQUFFO29CQUNiZ0IsV0FBV0osT0FBT0ksU0FBUyxJQUFJO29CQUMvQkMsU0FBU0wsT0FBT0ssT0FBTyxJQUFJO29CQUMzQkMsT0FBT04sT0FBT00sS0FBSyxJQUFJO29CQUN2QixHQUFHTixPQUFPLHNDQUFzQztnQkFDcEQ7WUFDSjtZQUNBLE9BQU9EO1FBQ1gsR0FBRyxFQUFFO1FBRUwsT0FBTztZQUNIWCxJQUFJSCxTQUFTRyxFQUFFO1lBQ2ZvQixTQUFTdkIsU0FBU3dCLElBQUk7WUFDdEJDLFVBQVV6QixFQUFBQSxvQkFBQUEsU0FBU0UsTUFBTSxjQUFmRix3Q0FBQUEsa0JBQWlCRyxFQUFFLEtBQUk7WUFDakNELFFBQVFEO1lBQ1J5QixnQkFBZ0IxQixFQUFBQSwwQkFBQUEsU0FBUzJCLGFBQWEsY0FBdEIzQiwrQ0FBQUEsZ0NBQUFBLHdCQUF3QlcsS0FBSyxjQUE3QlgscURBQUFBLGlDQUFBQSw2QkFBK0IsQ0FBQyxFQUFFLGNBQWxDQSxxREFBQUEsK0JBQW9DRyxFQUFFLEtBQUk7WUFDMUR5QixjQUFjNUIsRUFBQUEsMkJBQUFBLFNBQVMyQixhQUFhLGNBQXRCM0IsZ0RBQUFBLGlDQUFBQSx5QkFBd0JXLEtBQUssY0FBN0JYLHFEQUFBQSw4QkFBK0IsQ0FBQyxFQUFFLEtBQUk7Z0JBQUVHLElBQUk7Z0JBQUdDLE9BQU87WUFBVTtZQUM5RU0sU0FBU0U7WUFDVGlCLFFBQVE7Z0JBQ0pDLE9BQU87Z0JBQ1BDLFdBQVc7Z0JBQ1hDLE9BQU87Z0JBQ1BDLG9CQUFvQjtZQUN4QjtZQUNBQyxVQUFVO1lBQ1ZDLGNBQWNuQztRQUNsQjtJQUNKO0FBQ0osRUFBQztBQUVEOzs7OztDQUtDLEdBQ00sTUFBTW9DLDhDQUE4QyxDQUN2REM7SUFFQSxJQUFJLENBQUNBLHVCQUF1QixDQUFDN0MsTUFBTUMsT0FBTyxDQUFDNEMsc0JBQXNCO1FBQzdELE9BQU8sRUFBRTtJQUNiO0lBRUEzQyxRQUFRQyxHQUFHLENBQUMsdUVBQTZEO1FBQ3JFQyxjQUFjeUMsb0JBQW9CeEMsTUFBTTtRQUN4Q0MsY0FBY3VDLG1CQUFtQixDQUFDLEVBQUU7SUFDeEM7SUFFQSxxREFBcUQ7SUFDckQsbUVBQW1FO0lBQ25FLE1BQU1DLGVBQWVELG9CQUFvQkUsTUFBTSxDQUFDLENBQUNDO1lBQzdDQSxtQ0FBQUEsNkJBQUFBO2dCQUFBQSxlQUFBQSxLQUFLdEMsTUFBTSxjQUFYc0Msb0NBQUFBLDhCQUFBQSxhQUFhQyxjQUFjLGNBQTNCRCxtREFBQUEsb0NBQUFBLDRCQUE2QjdCLEtBQUssY0FBbEM2Qix3REFBQUEsa0NBQW9DRSxJQUFJLENBQUMsQ0FBQ3hCO1lBQ3RDLE9BQU9BLEVBQUVmLEVBQUUsS0FBS3FDLEtBQUtHLFFBQVE7UUFDakM7O0lBR0pqRCxRQUFRQyxHQUFHLENBQUMscUVBQTJEO1FBQ25FaUQsZUFBZVAsb0JBQW9CeEMsTUFBTTtRQUN6Q2dELGVBQWVQLGFBQWF6QyxNQUFNO1FBQ2xDaUQsY0FBY1Qsb0JBQW9CeEMsTUFBTSxHQUFHeUMsYUFBYXpDLE1BQU07SUFDbEU7SUFFQSw0QkFBNEI7SUFDNUIsTUFBTWtELGdCQUFnQlQsYUFBYXZDLEdBQUcsQ0FBQyxDQUFDaUQ7UUFDcEMsTUFBTW5CLFNBQVMxQyxrRUFBd0JBLENBQUM2RDtRQUN4QyxPQUFPO1lBQUUsR0FBR0EsR0FBRztZQUFFbkI7UUFBTztJQUM1QjtJQUVBbkMsUUFBUUMsR0FBRyxDQUFDLGdFQUFzRDtRQUM5REMsY0FBY21ELGNBQWNsRCxNQUFNO1FBQ2xDb0QsaUJBQWlCRixjQUFjbEMsTUFBTSxDQUFDLENBQUNDLEtBQVUwQjtZQUM3QyxNQUFNVSxNQUFNVixLQUFLWCxNQUFNLENBQUNFLFNBQVMsR0FBRyxZQUN6QlMsS0FBS1gsTUFBTSxDQUFDSSxrQkFBa0IsR0FBRyxhQUFhO1lBQ3pEbkIsR0FBRyxDQUFDb0MsSUFBSSxHQUFHLENBQUNwQyxHQUFHLENBQUNvQyxJQUFJLElBQUksS0FBSztZQUM3QixPQUFPcEM7UUFDWCxHQUFHLENBQUM7SUFDUjtJQUVBLG9EQUFvRDtJQUNwRCx1Q0FBdUM7SUFDdkMsTUFBTXFDLGNBQWNKLGNBQWNsQyxNQUFNLENBQ3BDLENBQUNDLEtBQVVrQztRQUNQLE1BQU1FLE1BQU0sR0FBbUJGLE9BQWhCQSxJQUFJdkIsUUFBUSxFQUFDLEtBQXlCdUIsT0FBdEJBLElBQUl0QixjQUFjLEVBQUMsS0FBZSxPQUFac0IsSUFBSXpCLE9BQU87UUFDaEUsSUFBSSxDQUFDVCxHQUFHLENBQUNvQyxJQUFJLEVBQUU7Z0JBU2VGO1lBUjFCbEMsR0FBRyxDQUFDb0MsSUFBSSxHQUFHO2dCQUNQL0MsSUFBSTZDLElBQUk3QyxFQUFFO2dCQUNWc0IsVUFBVXVCLElBQUl2QixRQUFRO2dCQUN0QnZCLFFBQVE4QyxJQUFJOUMsTUFBTTtnQkFDbEJ3QixnQkFBZ0JzQixJQUFJdEIsY0FBYztnQkFDbENFLGNBQWNvQixJQUFJcEIsWUFBWTtnQkFDOUJMLFNBQVN5QixJQUFJekIsT0FBTztnQkFDcEJNLFFBQVFtQixJQUFJbkIsTUFBTTtnQkFDbEJ2QixvQkFBb0IsR0FBRTBDLHVCQUFBQSxJQUFJSSxlQUFlLGNBQW5CSiwyQ0FBQUEscUJBQXFCMUMsb0JBQW9CO2dCQUMvREksU0FBUyxFQUFFO1lBQ2Y7UUFDSjtRQUNBSSxHQUFHLENBQUNvQyxJQUFJLENBQUN4QyxPQUFPLENBQUNZLElBQUksQ0FBQzBCLElBQUlqQyxNQUFNO1FBQ2hDLE9BQU9EO0lBQ1gsR0FDQSxDQUFDO0lBR0xwQixRQUFRQyxHQUFHLENBQUMscUZBQTJFO1FBQ25GMEQsWUFBWUMsT0FBT0MsSUFBSSxDQUFDSixhQUFhdEQsTUFBTTtRQUMzQzJELFdBQVdGLE9BQU9DLElBQUksQ0FBQ0o7SUFDM0I7SUFFQSw2REFBNkQ7SUFDN0QsTUFBTU0sYUFBYUgsT0FBT0ksTUFBTSxDQUFDUCxhQUFhcEQsR0FBRyxDQUFDLENBQUM0RDtRQUMvQyxNQUFNQyxnQkFBZ0JELE1BQU1qRCxPQUFPLENBQUNHLE1BQU0sQ0FDdEMsQ0FBQ0MsS0FBVUM7WUFDUCxNQUFNQyxpQkFBaUJGLElBQUlHLElBQUksQ0FDM0IsQ0FBQ0MsSUFBV0EsRUFBRWYsRUFBRSxLQUFLWSxPQUFPWixFQUFFO1lBRWxDLElBQUlhLGdCQUFnQjtnQkFDaEIsaURBQWlEO2dCQUNqREEsZUFBZUcsU0FBUyxHQUFHSixPQUFPSSxTQUFTLElBQUlILGVBQWVHLFNBQVM7Z0JBQ3ZFSCxlQUFlSSxPQUFPLEdBQUdMLE9BQU9LLE9BQU8sSUFBSUosZUFBZUksT0FBTztnQkFDakVKLGVBQWVLLEtBQUssR0FBR04sT0FBT00sS0FBSyxJQUFJTCxlQUFlSyxLQUFLO1lBQy9ELE9BQU87Z0JBQ0gsc0NBQXNDO2dCQUN0Q1AsSUFBSVEsSUFBSSxDQUFDO29CQUNMbkIsSUFBSVksT0FBT1osRUFBRTtvQkFDYmdCLFdBQVdKLE9BQU9JLFNBQVMsSUFBSTtvQkFDL0JDLFNBQVNMLE9BQU9LLE9BQU8sSUFBSTtvQkFDM0JDLE9BQU9OLE9BQU9NLEtBQUssSUFBSTtvQkFDdkIsR0FBR04sT0FBTyxzQ0FBc0M7Z0JBQ3BEO1lBQ0o7WUFDQSxPQUFPRDtRQUNYLEdBQ0EsRUFBRTtRQUdOLHFDQUFxQztRQUNyQyxJQUFJb0I7UUFDSixJQUFJeUIsTUFBTTlCLE1BQU0sQ0FBQ0UsU0FBUyxFQUFFO1lBQ3hCRyxXQUFXO1FBQ2YsT0FBTyxJQUFJeUIsTUFBTTlCLE1BQU0sQ0FBQ0ksa0JBQWtCLEVBQUU7WUFDeENDLFdBQVc7UUFDZixPQUFPO1lBQ0hBLFdBQVcsV0FBVywrQkFBK0I7O1FBQ3pEO1FBRUEsaURBQWlEO1FBQ2pELE1BQU0yQixpQkFBaUJGLE1BQU16RCxNQUFNLElBQUk7WUFBRUMsSUFBSTtZQUFHQyxPQUFPO1FBQVU7UUFFakUsMENBQTBDO1FBQzFDLElBQUl1RCxNQUFNckQsb0JBQW9CLElBQUksQ0FBQ3VELGVBQWV2RCxvQkFBb0IsRUFBRTtZQUNwRXVELGVBQWV2RCxvQkFBb0IsR0FBR3FELE1BQU1yRCxvQkFBb0I7UUFDcEU7UUFFQSxPQUFPO1lBQ0hILElBQUl3RCxNQUFNeEQsRUFBRTtZQUNab0IsU0FBU29DLE1BQU1wQyxPQUFPO1lBQ3RCRSxVQUFVa0MsTUFBTWxDLFFBQVE7WUFDeEJ2QixRQUFRMkQ7WUFDUm5DLGdCQUFnQmlDLE1BQU1qQyxjQUFjO1lBQ3BDRSxjQUFjK0IsTUFBTS9CLFlBQVksSUFBSTtnQkFBRXpCLElBQUk7Z0JBQUdDLE9BQU87WUFBVTtZQUM5RE0sU0FBU2tEO1lBQ1QvQixRQUFROEIsTUFBTTlCLE1BQU07WUFDcEJLO1lBQ0FDLGNBQWN3QjtRQUNsQjtJQUNKO0lBRUFqRSxRQUFRQyxHQUFHLENBQUMsOERBQW9EO1FBQzVEQyxjQUFjNkQsV0FBVzVELE1BQU07UUFDL0JpRSxtQkFBbUJMLFdBQVc1QyxNQUFNLENBQUMsQ0FBQ0MsS0FBVTBCO1lBQzVDMUIsR0FBRyxDQUFDMEIsS0FBS04sUUFBUSxDQUFDLEdBQUcsQ0FBQ3BCLEdBQUcsQ0FBQzBCLEtBQUtOLFFBQVEsQ0FBQyxJQUFJLEtBQUs7WUFDakQsT0FBT3BCO1FBQ1gsR0FBRyxDQUFDO1FBQ0poQixjQUFjMkQsVUFBVSxDQUFDLEVBQUU7SUFDL0I7SUFFQSxPQUFPQTtBQUNYLEVBQUM7QUFFRDs7OztDQUlDLEdBQ0QsTUFBTU0sc0JBQXNCLENBQUMvRDtJQUN6QixPQUFRQSxTQUFTa0MsUUFBUTtRQUNyQixLQUFLO1lBQ0Q7UUFDSixLQUFLO1lBQ0Q7UUFDSixLQUFLO1lBQ0Q7UUFDSjtZQUNJO0lBQ1I7QUFDSjtBQUVBOzs7O0NBSUMsR0FDTSxNQUFNOEIsMEJBQTBCLENBQUNDO0lBQ3BDLE1BQU1DLE9BQU8sSUFBSUM7SUFDakIsT0FBT0YsS0FBSzFCLE1BQU0sQ0FBQ0MsQ0FBQUE7UUFDZixJQUFJMEIsS0FBS0UsR0FBRyxDQUFDNUIsS0FBS3JDLEVBQUUsR0FBRztZQUNuQixPQUFPO1FBQ1g7UUFDQStELEtBQUtHLEdBQUcsQ0FBQzdCLEtBQUtyQyxFQUFFO1FBQ2hCLE9BQU87SUFDWDtBQUNKLEVBQUM7QUFFRDs7OztDQUlDLEdBQ00sTUFBTW1FLDBCQUEwQixDQUFDTDtJQUNwQyxPQUFPQSxLQUFLTSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0M7UUFDakIsMERBQTBEO1FBQzFELE1BQU1DLGVBQWVYLG9CQUFvQlMsS0FBS1Qsb0JBQW9CVTtRQUNsRSxJQUFJQyxpQkFBaUIsR0FBRztZQUNwQixPQUFPQTtRQUNYO1FBRUEscUNBQXFDO1FBQ3JDLE1BQU1DLFFBQVEsSUFBSUMsS0FBS0osRUFBRWpELE9BQU8sRUFBRXNELE9BQU87UUFDekMsTUFBTUMsUUFBUSxJQUFJRixLQUFLSCxFQUFFbEQsT0FBTyxFQUFFc0QsT0FBTztRQUV6QyxJQUFJTCxFQUFFdEMsUUFBUSxLQUFLLFdBQVc7WUFDMUIsNERBQTREO1lBQzVELE9BQU95QyxRQUFRRztRQUNuQixPQUFPLElBQUlOLEVBQUV0QyxRQUFRLEtBQUssWUFBWTtZQUNsQyx1Q0FBdUM7WUFDdkMsT0FBT3lDLFFBQVFHO1FBQ25CLE9BQU87WUFDSCxrRUFBa0U7WUFDbEUsT0FBT0EsUUFBUUg7UUFDbkI7SUFDSjtBQUNKLEVBQUM7QUFFRDs7OztDQUlDLEdBQ00sTUFBTUksK0JBQStCO1FBQUMsRUFDekMxQyxzQkFBc0IsRUFBRSxFQUN4QjJDLHdCQUF3QixFQUFFLEVBQzFCekYsaUJBQWlCLEVBQ2pCMEYsbUJBQW1CLElBQUksRUFDdkJDLFFBQVEsS0FBSyxFQU9oQjtJQUNHLElBQUk7UUFDQSx3Q0FBd0M7UUFFeEMsMkNBQTJDO1FBQzNDLE1BQU1DLGtCQUFrQi9DLDRDQUE0Q0M7UUFFcEUsaURBQWlEO1FBQ2pELE1BQU0rQyx1QkFBdUJILG1CQUN2QjVGLDBDQUEwQzJGLHVCQUF1QnpGLHFCQUNqRSxFQUFFO1FBRVIsbUJBQW1CO1FBQ25CLE1BQU04RixlQUFlO2VBQUlGO2VBQW9CQztTQUFxQjtRQUVsRSx3Q0FBd0M7UUFFeEMsMERBQTBEO1FBQzFELE1BQU1FLG1CQUFtQnRCLHdCQUF3QnFCO1FBQ2pELE1BQU1FLGFBQWFqQix3QkFBd0JnQjtRQUUzQyx3Q0FBd0M7UUFFeEMsMEJBQTBCO1FBQzFCLElBQUlKLE9BQU87WUFDUE0sa0JBQWtCRCxZQUFZO1FBQ2xDO1FBRUEsT0FBT0E7SUFDWCxFQUFFLE9BQU9oRixPQUFPO1FBQ1piLFFBQVFhLEtBQUssQ0FBQyxtREFBbURBO1FBQ2pFLE9BQU8sRUFBRTtJQUNiO0FBQ0osRUFBQztBQUVEOzs7OztDQUtDLEdBQ00sTUFBTWtGLCtCQUErQixDQUN4Q3hCLE1BQ0F5QjtJQUVBLE9BQU96QixLQUFLMUIsTUFBTSxDQUFDQyxDQUFBQSxPQUFRa0QsV0FBV0MsUUFBUSxDQUFDbkQsS0FBS04sUUFBUTtBQUNoRSxFQUFDO0FBRUQ7Ozs7Q0FJQyxHQUNNLE1BQU0wRCx1QkFBdUIsQ0FBQzNCO0lBQ2pDLE9BQU87UUFDSDRCLE9BQU81QixLQUFLcEUsTUFBTTtRQUNsQmlHLFNBQVM3QixLQUFLMUIsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLTixRQUFRLEtBQUssV0FBV3JDLE1BQU07UUFDaEVrRyxVQUFVOUIsS0FBSzFCLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS04sUUFBUSxLQUFLLFlBQVlyQyxNQUFNO1FBQ2xFbUcsV0FBVy9CLEtBQUsxQixNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtOLFFBQVEsS0FBSyxhQUFhckMsTUFBTTtJQUN4RTtBQUNKLEVBQUM7QUFFRDs7Ozs7Q0FLQyxHQUNNLE1BQU0yRixvQkFBb0IsU0FBQ3ZCO1FBQTZCbkMseUVBQWdCO0lBQzNFLElBQUltRSxLQUF5QixFQUFlO0lBRTVDLE1BQU1DLE1BQU1qQyxLQUFLbEUsR0FBRyxDQUFDeUMsQ0FBQUEsT0FBUUEsS0FBS3JDLEVBQUU7SUFDcEMsTUFBTWdHLGVBQWVELElBQUkzRCxNQUFNLENBQUMsQ0FBQ3BDLElBQUlpRyxRQUFVRixJQUFJRyxPQUFPLENBQUNsRyxRQUFRaUc7SUFFbkUxRyxRQUFRaUUsS0FBSyxDQUFDLGdCQUFZLE9BQU43QixPQUFNO0lBQzFCcEMsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQnNFLEtBQUtwRSxNQUFNO0lBQ3pDSCxRQUFRQyxHQUFHLENBQUMsZUFBZXNFLEtBQUtwRCxNQUFNLENBQUMsQ0FBQ0MsS0FBSzBCO1FBQ3pDMUIsR0FBRyxDQUFDMEIsS0FBS04sUUFBUSxDQUFDLEdBQUcsQ0FBQ3BCLEdBQUcsQ0FBQzBCLEtBQUtOLFFBQVEsQ0FBQyxJQUFJLEtBQUs7UUFDakQsT0FBT3BCO0lBQ1gsR0FBRyxDQUFDO0lBRUosSUFBSXFGLGFBQWF0RyxNQUFNLEdBQUcsR0FBRztRQUN6QkgsUUFBUWMsSUFBSSxDQUFDLDJCQUEyQmhCLE1BQU04RyxJQUFJLENBQUMsSUFBSW5DLElBQUlnQztRQUMzRHpHLFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0JzRSxLQUFLMUIsTUFBTSxDQUFDQyxDQUFBQSxPQUFRMkQsYUFBYVIsUUFBUSxDQUFDbkQsS0FBS3JDLEVBQUU7SUFDdkYsT0FBTztRQUNIVCxRQUFRQyxHQUFHLENBQUM7SUFDaEI7SUFFQUQsUUFBUUMsR0FBRyxDQUFDO0lBQ1osTUFBTStGLGFBQWE7UUFBQztRQUFXO1FBQVk7S0FBWTtJQUN2REEsV0FBV2EsT0FBTyxDQUFDckUsQ0FBQUE7UUFDZixNQUFNc0UsU0FBU3ZDLEtBQUtoRCxJQUFJLENBQUN1QixDQUFBQSxPQUFRQSxLQUFLTixRQUFRLEtBQUtBO1FBQ25ELElBQUlzRSxRQUFRO2dCQUdVQSxzQkFFTkEsZ0JBQ01BO1lBTGxCOUcsUUFBUUMsR0FBRyxDQUFDLEdBQVksT0FBVHVDLFVBQVMsTUFBSTtnQkFDeEIvQixJQUFJcUcsT0FBT3JHLEVBQUU7Z0JBQ2J5QixZQUFZLEdBQUU0RSx1QkFBQUEsT0FBTzVFLFlBQVksY0FBbkI0RSwyQ0FBQUEscUJBQXFCcEcsS0FBSztnQkFDeENtQixTQUFTaUYsT0FBT2pGLE9BQU87Z0JBQ3ZCckIsTUFBTSxHQUFFc0csaUJBQUFBLE9BQU90RyxNQUFNLGNBQWJzRyxxQ0FBQUEsZUFBZXBHLEtBQUs7Z0JBQzVCcUcsY0FBY0QsRUFBQUEsa0JBQUFBLE9BQU85RixPQUFPLGNBQWQ4RixzQ0FBQUEsZ0JBQWdCM0csTUFBTSxLQUFJO1lBQzVDO1FBQ0o7SUFDSjtJQUNBSCxRQUFRZ0gsUUFBUTtBQUNwQixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvbGliL2NyZXctdHJhaW5pbmctdXRpbHMudHM/ZjM0YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHZXRUcmFpbmluZ1Nlc3Npb25TdGF0dXMgfSBmcm9tICcuL2FjdGlvbnMnXG5cbi8qKlxuICogVW5pZmllZCB0cmFpbmluZyBkYXRhIGludGVyZmFjZSB0aGF0IGNvbWJpbmVzIG92ZXJkdWUsIHVwY29taW5nLCBhbmQgY29tcGxldGVkIHRyYWluaW5nIGRhdGFcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBVbmlmaWVkVHJhaW5pbmdEYXRhIHtcbiAgICBpZDogbnVtYmVyXG4gICAgZHVlRGF0ZTogc3RyaW5nIC8vIEZvciBvdmVyZHVlL3VwY29taW5nIHRoaXMgaXMgdGhlIGR1ZSBkYXRlLCBmb3IgY29tcGxldGVkIHRoaXMgaXMgdGhlIGNvbXBsZXRpb24gZGF0ZVxuICAgIHZlc3NlbElEOiBudW1iZXJcbiAgICB2ZXNzZWw6IHtcbiAgICAgICAgaWQ6IG51bWJlclxuICAgICAgICB0aXRsZTogc3RyaW5nXG4gICAgICAgIFtrZXk6IHN0cmluZ106IGFueSAvLyBBbGxvdyBmb3IgYWRkaXRpb25hbCB2ZXNzZWwgcHJvcGVydGllcyBsaWtlIHBvc2l0aW9uLCBldGMuXG4gICAgfVxuICAgIHRyYWluaW5nVHlwZUlEOiBudW1iZXJcbiAgICB0cmFpbmluZ1R5cGU6IHtcbiAgICAgICAgaWQ6IG51bWJlclxuICAgICAgICB0aXRsZTogc3RyaW5nXG4gICAgfVxuICAgIG1lbWJlcnM6IEFycmF5PHtcbiAgICAgICAgaWQ6IG51bWJlclxuICAgICAgICBmaXJzdE5hbWU/OiBzdHJpbmdcbiAgICAgICAgc3VybmFtZT86IHN0cmluZ1xuICAgIH0+XG4gICAgc3RhdHVzOiB7XG4gICAgICAgIGNsYXNzOiBzdHJpbmdcbiAgICAgICAgbGFiZWw6IHN0cmluZ1xuICAgICAgICBpc092ZXJkdWU6IGJvb2xlYW5cbiAgICAgICAgZHVlV2l0aGluU2V2ZW5EYXlzOiBib29sZWFuXG4gICAgfVxuICAgIGNhdGVnb3J5OiAnb3ZlcmR1ZScgfCAndXBjb21pbmcnIHwgJ2NvbXBsZXRlZCcgLy8gQWRkZWQgdG8gaGVscCB3aXRoIHNvcnRpbmcgYW5kIGRpc3BsYXlcbiAgICBvcmlnaW5hbERhdGE/OiBhbnkgLy8gU3RvcmUgb3JpZ2luYWwgZGF0YSBmb3IgcmVmZXJlbmNlIGlmIG5lZWRlZFxufVxuXG4vKipcbiAqIFRyYWluaW5nIHByaW9yaXR5IGxldmVscyBmb3Igc29ydGluZ1xuICovXG5leHBvcnQgZW51bSBUcmFpbmluZ1ByaW9yaXR5IHtcbiAgICBPVkVSRFVFID0gMSxcbiAgICBVUENPTUlORyA9IDIsXG4gICAgQ09NUExFVEVEID0gM1xufVxuXG4vKipcbiAqIFRyYW5zZm9ybSBjb21wbGV0ZWQgdHJhaW5pbmcgc2Vzc2lvbnMgdG8gbWF0Y2ggdGhlIHVuaWZpZWQgdHJhaW5pbmcgZGF0YSBmb3JtYXRcbiAqIEBwYXJhbSB0cmFpbmluZ0xpc3QgLSBBcnJheSBvZiBjb21wbGV0ZWQgdHJhaW5pbmcgc2Vzc2lvbnNcbiAqIEBwYXJhbSBnZXRWZXNzZWxXaXRoSWNvbiAtIEZ1bmN0aW9uIHRvIGdldCBjb21wbGV0ZSB2ZXNzZWwgZGF0YSB3aXRoIHBvc2l0aW9uL2ljb25cbiAqIEByZXR1cm5zIEFycmF5IG9mIHRyYW5zZm9ybWVkIHRyYWluaW5nIGRhdGFcbiAqL1xuZXhwb3J0IGNvbnN0IHRyYW5zZm9ybUNvbXBsZXRlZFRyYWluaW5nVG9VbmlmaWVkRm9ybWF0ID0gKFxuICAgIHRyYWluaW5nTGlzdDogYW55W10sXG4gICAgZ2V0VmVzc2VsV2l0aEljb24/OiAoaWQ6IGFueSwgdmVzc2VsOiBhbnkpID0+IGFueVxuKTogVW5pZmllZFRyYWluaW5nRGF0YVtdID0+IHtcbiAgICBpZiAoIXRyYWluaW5nTGlzdCB8fCAhQXJyYXkuaXNBcnJheSh0cmFpbmluZ0xpc3QpKSB7XG4gICAgICAgIHJldHVybiBbXVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfwn5SNIFtjcmV3LXRyYWluaW5nLXV0aWxzXSBUcmFuc2Zvcm1pbmcgY29tcGxldGVkIHRyYWluaW5nIGRhdGE6Jywge1xuICAgICAgICB0b3RhbFJlY29yZHM6IHRyYWluaW5nTGlzdC5sZW5ndGgsXG4gICAgICAgIHNhbXBsZVJlY29yZDogdHJhaW5pbmdMaXN0WzBdXG4gICAgfSlcblxuICAgIHJldHVybiB0cmFpbmluZ0xpc3QubWFwKCh0cmFpbmluZzogYW55KSA9PiB7XG4gICAgICAgIC8vIEVuaGFuY2VkIHZlc3NlbCBkYXRhIHRyYW5zZm9ybWF0aW9uIHdpdGggcG9zaXRpb24gaW5mb3JtYXRpb25cbiAgICAgICAgbGV0IGNvbXBsZXRlVmVzc2VsRGF0YSA9IHRyYWluaW5nLnZlc3NlbCB8fCB7IGlkOiAwLCB0aXRsZTogJ1Vua25vd24nIH1cblxuICAgICAgICBpZiAoZ2V0VmVzc2VsV2l0aEljb24gJiYgdHJhaW5pbmcudmVzc2VsPy5pZCkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAvLyBHZXQgY29tcGxldGUgdmVzc2VsIGRhdGEgaW5jbHVkaW5nIHBvc2l0aW9uLCBpY29uLCBhbmQgb3RoZXIgbWV0YWRhdGFcbiAgICAgICAgICAgICAgICBjb21wbGV0ZVZlc3NlbERhdGEgPSBnZXRWZXNzZWxXaXRoSWNvbih0cmFpbmluZy52ZXNzZWwuaWQsIHRyYWluaW5nLnZlc3NlbClcblxuICAgICAgICAgICAgICAgIC8vIEVuc3VyZSB3ZSBwcmVzZXJ2ZSBvcmlnaW5hbCB2ZXNzZWwgZGF0YSBpZiB0cmFuc2Zvcm1hdGlvbiBmYWlsc1xuICAgICAgICAgICAgICAgIGlmICghY29tcGxldGVWZXNzZWxEYXRhIHx8IHR5cGVvZiBjb21wbGV0ZVZlc3NlbERhdGEgIT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbXBsZXRlVmVzc2VsRGF0YSA9IHRyYWluaW5nLnZlc3NlbFxuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC8vIEFkZCBwb3NpdGlvbiBpbmZvcm1hdGlvbiBpZiBhdmFpbGFibGVcbiAgICAgICAgICAgICAgICBpZiAodHJhaW5pbmcudmVzc2VsLnBvc2l0aW9uICYmICFjb21wbGV0ZVZlc3NlbERhdGEucG9zaXRpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgY29tcGxldGVWZXNzZWxEYXRhLnBvc2l0aW9uID0gdHJhaW5pbmcudmVzc2VsLnBvc2l0aW9uXG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgLy8gQWRkIGxvY2F0aW9uIHR5cGUgaWYgYXZhaWxhYmxlXG4gICAgICAgICAgICAgICAgaWYgKHRyYWluaW5nLnRyYWluaW5nTG9jYXRpb25UeXBlICYmICFjb21wbGV0ZVZlc3NlbERhdGEudHJhaW5pbmdMb2NhdGlvblR5cGUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29tcGxldGVWZXNzZWxEYXRhLnRyYWluaW5nTG9jYXRpb25UeXBlID0gdHJhaW5pbmcudHJhaW5pbmdMb2NhdGlvblR5cGVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIGVuaGFuY2UgdmVzc2VsIGRhdGEgZm9yIHRyYWluaW5nOicsIHRyYWluaW5nLmlkLCBlcnJvcilcbiAgICAgICAgICAgICAgICBjb21wbGV0ZVZlc3NlbERhdGEgPSB0cmFpbmluZy52ZXNzZWxcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEVuaGFuY2VkIG1lbWJlciBkZWR1cGxpY2F0aW9uIGFuZCBub3JtYWxpemF0aW9uXG4gICAgICAgIGNvbnN0IHJhd01lbWJlcnMgPSB0cmFpbmluZy5tZW1iZXJzPy5ub2RlcyB8fCBbXVxuICAgICAgICBjb25zdCBkZWR1cGxpY2F0ZWRNZW1iZXJzID0gcmF3TWVtYmVycy5yZWR1Y2UoKGFjYzogYW55W10sIG1lbWJlcjogYW55KSA9PiB7XG4gICAgICAgICAgICAvLyBDaGVjayBpZiBtZW1iZXIgYWxyZWFkeSBleGlzdHMgaW4gdGhlIGFjY3VtdWxhdG9yXG4gICAgICAgICAgICBjb25zdCBleGlzdGluZ01lbWJlciA9IGFjYy5maW5kKG0gPT4gbS5pZCA9PT0gbWVtYmVyLmlkKVxuXG4gICAgICAgICAgICBpZiAoZXhpc3RpbmdNZW1iZXIpIHtcbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgZXhpc3RpbmcgbWVtYmVyIHdpdGggbW9yZSBjb21wbGV0ZSBkYXRhXG4gICAgICAgICAgICAgICAgZXhpc3RpbmdNZW1iZXIuZmlyc3ROYW1lID0gbWVtYmVyLmZpcnN0TmFtZSB8fCBleGlzdGluZ01lbWJlci5maXJzdE5hbWVcbiAgICAgICAgICAgICAgICBleGlzdGluZ01lbWJlci5zdXJuYW1lID0gbWVtYmVyLnN1cm5hbWUgfHwgZXhpc3RpbmdNZW1iZXIuc3VybmFtZVxuICAgICAgICAgICAgICAgIGV4aXN0aW5nTWVtYmVyLmVtYWlsID0gbWVtYmVyLmVtYWlsIHx8IGV4aXN0aW5nTWVtYmVyLmVtYWlsXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIEFkZCBuZXcgbWVtYmVyIHdpdGggbm9ybWFsaXplZCBkYXRhXG4gICAgICAgICAgICAgICAgYWNjLnB1c2goe1xuICAgICAgICAgICAgICAgICAgICBpZDogbWVtYmVyLmlkLFxuICAgICAgICAgICAgICAgICAgICBmaXJzdE5hbWU6IG1lbWJlci5maXJzdE5hbWUgfHwgJycsXG4gICAgICAgICAgICAgICAgICAgIHN1cm5hbWU6IG1lbWJlci5zdXJuYW1lIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgICBlbWFpbDogbWVtYmVyLmVtYWlsIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgICAuLi5tZW1iZXIgLy8gUHJlc2VydmUgYW55IGFkZGl0aW9uYWwgbWVtYmVyIGRhdGFcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGFjY1xuICAgICAgICB9LCBbXSlcblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgaWQ6IHRyYWluaW5nLmlkLFxuICAgICAgICAgICAgZHVlRGF0ZTogdHJhaW5pbmcuZGF0ZSwgLy8gTWFwIGNvbXBsZXRpb24gZGF0ZSB0byBkdWVEYXRlIGZvciB1bmlmaWVkIHNvcnRpbmdcbiAgICAgICAgICAgIHZlc3NlbElEOiB0cmFpbmluZy52ZXNzZWw/LmlkIHx8IDAsXG4gICAgICAgICAgICB2ZXNzZWw6IGNvbXBsZXRlVmVzc2VsRGF0YSxcbiAgICAgICAgICAgIHRyYWluaW5nVHlwZUlEOiB0cmFpbmluZy50cmFpbmluZ1R5cGVzPy5ub2Rlcz8uWzBdPy5pZCB8fCAwLFxuICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiB0cmFpbmluZy50cmFpbmluZ1R5cGVzPy5ub2Rlcz8uWzBdIHx8IHsgaWQ6IDAsIHRpdGxlOiAnVW5rbm93bicgfSxcbiAgICAgICAgICAgIG1lbWJlcnM6IGRlZHVwbGljYXRlZE1lbWJlcnMsXG4gICAgICAgICAgICBzdGF0dXM6IHtcbiAgICAgICAgICAgICAgICBsYWJlbDogJ0NvbXBsZXRlZCcsXG4gICAgICAgICAgICAgICAgaXNPdmVyZHVlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBjbGFzczogJ2JvcmRlciByb3VuZGVkIGJvcmRlci1ib3JkZXIgdGV4dC1pbnB1dCBiZy1vdXRlci1zcGFjZS01MCBwLTIgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyJyxcbiAgICAgICAgICAgICAgICBkdWVXaXRoaW5TZXZlbkRheXM6IGZhbHNlLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGNhdGVnb3J5OiAnY29tcGxldGVkJyBhcyBjb25zdCxcbiAgICAgICAgICAgIG9yaWdpbmFsRGF0YTogdHJhaW5pbmdcbiAgICAgICAgfVxuICAgIH0pXG59XG5cbi8qKlxuICogVHJhbnNmb3JtIHRyYWluaW5nIHNlc3Npb24gZHVlcyB0byB1bmlmaWVkIGZvcm1hdCB3aXRoIGNhbGN1bGF0ZWQgc3RhdHVzXG4gKiBBcHBsaWVzIHRoZSBzYW1lIGdyb3VwaW5nIGxvZ2ljIGFzIENyZXdUcmFpbmluZ0xpc3RcbiAqIEBwYXJhbSB0cmFpbmluZ1Nlc3Npb25EdWVzIC0gQXJyYXkgb2YgdHJhaW5pbmcgc2Vzc2lvbiBkdWVzIChvdmVyZHVlL3VwY29taW5nKVxuICogQHJldHVybnMgQXJyYXkgb2YgdHJhbnNmb3JtZWQgdHJhaW5pbmcgZGF0YSB3aXRoIGNhbGN1bGF0ZWQgc3RhdHVzXG4gKi9cbmV4cG9ydCBjb25zdCB0cmFuc2Zvcm1UcmFpbmluZ1Nlc3Npb25EdWVzVG9VbmlmaWVkRm9ybWF0ID0gKFxuICAgIHRyYWluaW5nU2Vzc2lvbkR1ZXM6IGFueVtdXG4pOiBVbmlmaWVkVHJhaW5pbmdEYXRhW10gPT4ge1xuICAgIGlmICghdHJhaW5pbmdTZXNzaW9uRHVlcyB8fCAhQXJyYXkuaXNBcnJheSh0cmFpbmluZ1Nlc3Npb25EdWVzKSkge1xuICAgICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygn8J+UjSBbY3Jldy10cmFpbmluZy11dGlsc10gUmF3IHRyYWluaW5nIHNlc3Npb24gZHVlcyBpbnB1dDonLCB7XG4gICAgICAgIHRvdGFsUmVjb3JkczogdHJhaW5pbmdTZXNzaW9uRHVlcy5sZW5ndGgsXG4gICAgICAgIHNhbXBsZVJlY29yZDogdHJhaW5pbmdTZXNzaW9uRHVlc1swXVxuICAgIH0pXG5cbiAgICAvLyBBcHBseSB0aGUgc2FtZSBmaWx0ZXJpbmcgbG9naWMgYXMgQ3Jld1RyYWluaW5nTGlzdFxuICAgIC8vIEZpbHRlciBvdXQgY3JldyBtZW1iZXJzIHdobyBhcmUgbm8gbG9uZ2VyIGFzc2lnbmVkIHRvIHRoZSB2ZXNzZWxcbiAgICBjb25zdCBmaWx0ZXJlZERhdGEgPSB0cmFpbmluZ1Nlc3Npb25EdWVzLmZpbHRlcigoaXRlbTogYW55KSA9PlxuICAgICAgICBpdGVtLnZlc3NlbD8uc2VhTG9nc01lbWJlcnM/Lm5vZGVzPy5zb21lKChtOiBhbnkpID0+IHtcbiAgICAgICAgICAgIHJldHVybiBtLmlkID09PSBpdGVtLm1lbWJlcklEXG4gICAgICAgIH0pLFxuICAgIClcblxuICAgIGNvbnNvbGUubG9nKCfwn5SNIFtjcmV3LXRyYWluaW5nLXV0aWxzXSBBZnRlciB2ZXNzZWwgbWVtYmVyIGZpbHRlcmluZzonLCB7XG4gICAgICAgIG9yaWdpbmFsQ291bnQ6IHRyYWluaW5nU2Vzc2lvbkR1ZXMubGVuZ3RoLFxuICAgICAgICBmaWx0ZXJlZENvdW50OiBmaWx0ZXJlZERhdGEubGVuZ3RoLFxuICAgICAgICByZW1vdmVkQ291bnQ6IHRyYWluaW5nU2Vzc2lvbkR1ZXMubGVuZ3RoIC0gZmlsdGVyZWREYXRhLmxlbmd0aFxuICAgIH0pXG5cbiAgICAvLyBBZGQgc3RhdHVzIHRvIGVhY2ggcmVjb3JkXG4gICAgY29uc3QgZHVlV2l0aFN0YXR1cyA9IGZpbHRlcmVkRGF0YS5tYXAoKGR1ZTogYW55KSA9PiB7XG4gICAgICAgIGNvbnN0IHN0YXR1cyA9IEdldFRyYWluaW5nU2Vzc2lvblN0YXR1cyhkdWUpXG4gICAgICAgIHJldHVybiB7IC4uLmR1ZSwgc3RhdHVzIH1cbiAgICB9KVxuXG4gICAgY29uc29sZS5sb2coJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIEFmdGVyIHN0YXR1cyBjYWxjdWxhdGlvbjonLCB7XG4gICAgICAgIHRvdGFsUmVjb3JkczogZHVlV2l0aFN0YXR1cy5sZW5ndGgsXG4gICAgICAgIHN0YXR1c0JyZWFrZG93bjogZHVlV2l0aFN0YXR1cy5yZWR1Y2UoKGFjYzogYW55LCBpdGVtOiBhbnkpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGtleSA9IGl0ZW0uc3RhdHVzLmlzT3ZlcmR1ZSA/ICdvdmVyZHVlJyA6XG4gICAgICAgICAgICAgICAgICAgICAgIGl0ZW0uc3RhdHVzLmR1ZVdpdGhpblNldmVuRGF5cyA/ICd1cGNvbWluZycgOiAnZnV0dXJlJ1xuICAgICAgICAgICAgYWNjW2tleV0gPSAoYWNjW2tleV0gfHwgMCkgKyAxXG4gICAgICAgICAgICByZXR1cm4gYWNjXG4gICAgICAgIH0sIHt9KVxuICAgIH0pXG5cbiAgICAvLyBBcHBseSB0aGUgc2FtZSBncm91cGluZyBsb2dpYyBhcyBDcmV3VHJhaW5pbmdMaXN0XG4gICAgLy8gR3JvdXAgYnkgdmVzc2VsLXRyYWluaW5nVHlwZS1kdWVEYXRlXG4gICAgY29uc3QgZ3JvdXBlZER1ZXMgPSBkdWVXaXRoU3RhdHVzLnJlZHVjZShcbiAgICAgICAgKGFjYzogYW55LCBkdWU6IGFueSkgPT4ge1xuICAgICAgICAgICAgY29uc3Qga2V5ID0gYCR7ZHVlLnZlc3NlbElEfS0ke2R1ZS50cmFpbmluZ1R5cGVJRH0tJHtkdWUuZHVlRGF0ZX1gXG4gICAgICAgICAgICBpZiAoIWFjY1trZXldKSB7XG4gICAgICAgICAgICAgICAgYWNjW2tleV0gPSB7XG4gICAgICAgICAgICAgICAgICAgIGlkOiBkdWUuaWQsXG4gICAgICAgICAgICAgICAgICAgIHZlc3NlbElEOiBkdWUudmVzc2VsSUQsXG4gICAgICAgICAgICAgICAgICAgIHZlc3NlbDogZHVlLnZlc3NlbCxcbiAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IGR1ZS50cmFpbmluZ1R5cGVJRCxcbiAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiBkdWUudHJhaW5pbmdUeXBlLFxuICAgICAgICAgICAgICAgICAgICBkdWVEYXRlOiBkdWUuZHVlRGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBkdWUuc3RhdHVzLFxuICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ0xvY2F0aW9uVHlwZTogZHVlLnRyYWluaW5nU2Vzc2lvbj8udHJhaW5pbmdMb2NhdGlvblR5cGUsXG4gICAgICAgICAgICAgICAgICAgIG1lbWJlcnM6IFtdLFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFjY1trZXldLm1lbWJlcnMucHVzaChkdWUubWVtYmVyKVxuICAgICAgICAgICAgcmV0dXJuIGFjY1xuICAgICAgICB9LFxuICAgICAgICB7fSxcbiAgICApXG5cbiAgICBjb25zb2xlLmxvZygn8J+UjSBbY3Jldy10cmFpbmluZy11dGlsc10gQWZ0ZXIgZ3JvdXBpbmcgYnkgdmVzc2VsLXRyYWluaW5nVHlwZS1kdWVEYXRlOicsIHtcbiAgICAgICAgZ3JvdXBDb3VudDogT2JqZWN0LmtleXMoZ3JvdXBlZER1ZXMpLmxlbmd0aCxcbiAgICAgICAgZ3JvdXBLZXlzOiBPYmplY3Qua2V5cyhncm91cGVkRHVlcylcbiAgICB9KVxuXG4gICAgLy8gTWVyZ2UgbWVtYmVycyB3aXRoaW4gZWFjaCBncm91cCAoc2FtZSBhcyBDcmV3VHJhaW5pbmdMaXN0KVxuICAgIGNvbnN0IG1lcmdlZER1ZXMgPSBPYmplY3QudmFsdWVzKGdyb3VwZWREdWVzKS5tYXAoKGdyb3VwOiBhbnkpID0+IHtcbiAgICAgICAgY29uc3QgbWVyZ2VkTWVtYmVycyA9IGdyb3VwLm1lbWJlcnMucmVkdWNlKFxuICAgICAgICAgICAgKGFjYzogYW55LCBtZW1iZXI6IGFueSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTWVtYmVyID0gYWNjLmZpbmQoXG4gICAgICAgICAgICAgICAgICAgIChtOiBhbnkpID0+IG0uaWQgPT09IG1lbWJlci5pZCxcbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgaWYgKGV4aXN0aW5nTWVtYmVyKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyBtZW1iZXIgd2l0aCBtb3JlIGNvbXBsZXRlIGRhdGFcbiAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdNZW1iZXIuZmlyc3ROYW1lID0gbWVtYmVyLmZpcnN0TmFtZSB8fCBleGlzdGluZ01lbWJlci5maXJzdE5hbWVcbiAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdNZW1iZXIuc3VybmFtZSA9IG1lbWJlci5zdXJuYW1lIHx8IGV4aXN0aW5nTWVtYmVyLnN1cm5hbWVcbiAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdNZW1iZXIuZW1haWwgPSBtZW1iZXIuZW1haWwgfHwgZXhpc3RpbmdNZW1iZXIuZW1haWxcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAvLyBBZGQgbmV3IG1lbWJlciB3aXRoIG5vcm1hbGl6ZWQgZGF0YVxuICAgICAgICAgICAgICAgICAgICBhY2MucHVzaCh7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZDogbWVtYmVyLmlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgZmlyc3ROYW1lOiBtZW1iZXIuZmlyc3ROYW1lIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgICAgICAgc3VybmFtZTogbWVtYmVyLnN1cm5hbWUgfHwgJycsXG4gICAgICAgICAgICAgICAgICAgICAgICBlbWFpbDogbWVtYmVyLmVtYWlsIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgICAgICAgLi4ubWVtYmVyIC8vIFByZXNlcnZlIGFueSBhZGRpdGlvbmFsIG1lbWJlciBkYXRhXG4gICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiBhY2NcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBbXSxcbiAgICAgICAgKVxuXG4gICAgICAgIC8vIERldGVybWluZSBjYXRlZ29yeSBiYXNlZCBvbiBzdGF0dXNcbiAgICAgICAgbGV0IGNhdGVnb3J5OiAnb3ZlcmR1ZScgfCAndXBjb21pbmcnIHwgJ2NvbXBsZXRlZCdcbiAgICAgICAgaWYgKGdyb3VwLnN0YXR1cy5pc092ZXJkdWUpIHtcbiAgICAgICAgICAgIGNhdGVnb3J5ID0gJ292ZXJkdWUnXG4gICAgICAgIH0gZWxzZSBpZiAoZ3JvdXAuc3RhdHVzLmR1ZVdpdGhpblNldmVuRGF5cykge1xuICAgICAgICAgICAgY2F0ZWdvcnkgPSAndXBjb21pbmcnXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjYXRlZ29yeSA9ICd1cGNvbWluZycgLy8gRGVmYXVsdCBmb3IgZnV0dXJlIGR1ZSBkYXRlc1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gRW5oYW5jZWQgdmVzc2VsIGRhdGEgd2l0aCBwb3NpdGlvbiBpbmZvcm1hdGlvblxuICAgICAgICBjb25zdCBlbmhhbmNlZFZlc3NlbCA9IGdyb3VwLnZlc3NlbCB8fCB7IGlkOiAwLCB0aXRsZTogJ1Vua25vd24nIH1cblxuICAgICAgICAvLyBBZGQgdHJhaW5pbmcgbG9jYXRpb24gdHlwZSBpZiBhdmFpbGFibGVcbiAgICAgICAgaWYgKGdyb3VwLnRyYWluaW5nTG9jYXRpb25UeXBlICYmICFlbmhhbmNlZFZlc3NlbC50cmFpbmluZ0xvY2F0aW9uVHlwZSkge1xuICAgICAgICAgICAgZW5oYW5jZWRWZXNzZWwudHJhaW5pbmdMb2NhdGlvblR5cGUgPSBncm91cC50cmFpbmluZ0xvY2F0aW9uVHlwZVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGlkOiBncm91cC5pZCxcbiAgICAgICAgICAgIGR1ZURhdGU6IGdyb3VwLmR1ZURhdGUsXG4gICAgICAgICAgICB2ZXNzZWxJRDogZ3JvdXAudmVzc2VsSUQsXG4gICAgICAgICAgICB2ZXNzZWw6IGVuaGFuY2VkVmVzc2VsLFxuICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IGdyb3VwLnRyYWluaW5nVHlwZUlELFxuICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiBncm91cC50cmFpbmluZ1R5cGUgfHwgeyBpZDogMCwgdGl0bGU6ICdVbmtub3duJyB9LFxuICAgICAgICAgICAgbWVtYmVyczogbWVyZ2VkTWVtYmVycyxcbiAgICAgICAgICAgIHN0YXR1czogZ3JvdXAuc3RhdHVzLFxuICAgICAgICAgICAgY2F0ZWdvcnksXG4gICAgICAgICAgICBvcmlnaW5hbERhdGE6IGdyb3VwXG4gICAgICAgIH1cbiAgICB9KVxuXG4gICAgY29uc29sZS5sb2coJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIEZpbmFsIG1lcmdlZCBkdWVzIGRhdGE6Jywge1xuICAgICAgICB0b3RhbFJlY29yZHM6IG1lcmdlZER1ZXMubGVuZ3RoLFxuICAgICAgICBjYXRlZ29yeUJyZWFrZG93bjogbWVyZ2VkRHVlcy5yZWR1Y2UoKGFjYzogYW55LCBpdGVtOiBhbnkpID0+IHtcbiAgICAgICAgICAgIGFjY1tpdGVtLmNhdGVnb3J5XSA9IChhY2NbaXRlbS5jYXRlZ29yeV0gfHwgMCkgKyAxXG4gICAgICAgICAgICByZXR1cm4gYWNjXG4gICAgICAgIH0sIHt9KSxcbiAgICAgICAgc2FtcGxlUmVjb3JkOiBtZXJnZWREdWVzWzBdXG4gICAgfSlcblxuICAgIHJldHVybiBtZXJnZWREdWVzXG59XG5cbi8qKlxuICogR2V0IHByaW9yaXR5IHZhbHVlIGZvciBzb3J0aW5nIGJhc2VkIG9uIHRyYWluaW5nIGNhdGVnb3J5IGFuZCBzdGF0dXNcbiAqIEBwYXJhbSB0cmFpbmluZyAtIFVuaWZpZWQgdHJhaW5pbmcgZGF0YSBpdGVtXG4gKiBAcmV0dXJucyBQcmlvcml0eSBudW1iZXIgKGxvd2VyID0gaGlnaGVyIHByaW9yaXR5KVxuICovXG5jb25zdCBnZXRUcmFpbmluZ1ByaW9yaXR5ID0gKHRyYWluaW5nOiBVbmlmaWVkVHJhaW5pbmdEYXRhKTogbnVtYmVyID0+IHtcbiAgICBzd2l0Y2ggKHRyYWluaW5nLmNhdGVnb3J5KSB7XG4gICAgICAgIGNhc2UgJ292ZXJkdWUnOlxuICAgICAgICAgICAgcmV0dXJuIFRyYWluaW5nUHJpb3JpdHkuT1ZFUkRVRVxuICAgICAgICBjYXNlICd1cGNvbWluZyc6XG4gICAgICAgICAgICByZXR1cm4gVHJhaW5pbmdQcmlvcml0eS5VUENPTUlOR1xuICAgICAgICBjYXNlICdjb21wbGV0ZWQnOlxuICAgICAgICAgICAgcmV0dXJuIFRyYWluaW5nUHJpb3JpdHkuQ09NUExFVEVEXG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICByZXR1cm4gVHJhaW5pbmdQcmlvcml0eS5DT01QTEVURURcbiAgICB9XG59XG5cbi8qKlxuICogUmVtb3ZlIGR1cGxpY2F0ZSB0cmFpbmluZyByZWNvcmRzIGJhc2VkIG9uIElEXG4gKiBAcGFyYW0gZGF0YSAtIEFycmF5IG9mIHVuaWZpZWQgdHJhaW5pbmcgZGF0YVxuICogQHJldHVybnMgRGVkdXBsaWNhdGVkIGFycmF5XG4gKi9cbmV4cG9ydCBjb25zdCBkZWR1cGxpY2F0ZVRyYWluaW5nRGF0YSA9IChkYXRhOiBVbmlmaWVkVHJhaW5pbmdEYXRhW10pOiBVbmlmaWVkVHJhaW5pbmdEYXRhW10gPT4ge1xuICAgIGNvbnN0IHNlZW4gPSBuZXcgU2V0PG51bWJlcj4oKVxuICAgIHJldHVybiBkYXRhLmZpbHRlcihpdGVtID0+IHtcbiAgICAgICAgaWYgKHNlZW4uaGFzKGl0ZW0uaWQpKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgICAgfVxuICAgICAgICBzZWVuLmFkZChpdGVtLmlkKVxuICAgICAgICByZXR1cm4gdHJ1ZVxuICAgIH0pXG59XG5cbi8qKlxuICogU29ydCB1bmlmaWVkIHRyYWluaW5nIGRhdGEgd2l0aCBwcmlvcml0eS1iYXNlZCBvcmRlcmluZ1xuICogQHBhcmFtIGRhdGEgLSBBcnJheSBvZiB1bmlmaWVkIHRyYWluaW5nIGRhdGFcbiAqIEByZXR1cm5zIFNvcnRlZCBhcnJheSB3aXRoIG92ZXJkdWUgZmlyc3QsIHRoZW4gdXBjb21pbmcsIHRoZW4gY29tcGxldGVkXG4gKi9cbmV4cG9ydCBjb25zdCBzb3J0VW5pZmllZFRyYWluaW5nRGF0YSA9IChkYXRhOiBVbmlmaWVkVHJhaW5pbmdEYXRhW10pOiBVbmlmaWVkVHJhaW5pbmdEYXRhW10gPT4ge1xuICAgIHJldHVybiBkYXRhLnNvcnQoKGEsIGIpID0+IHtcbiAgICAgICAgLy8gRmlyc3Qgc29ydCBieSBwcmlvcml0eSAob3ZlcmR1ZSA+IHVwY29taW5nID4gY29tcGxldGVkKVxuICAgICAgICBjb25zdCBwcmlvcml0eURpZmYgPSBnZXRUcmFpbmluZ1ByaW9yaXR5KGEpIC0gZ2V0VHJhaW5pbmdQcmlvcml0eShiKVxuICAgICAgICBpZiAocHJpb3JpdHlEaWZmICE9PSAwKSB7XG4gICAgICAgICAgICByZXR1cm4gcHJpb3JpdHlEaWZmXG4gICAgICAgIH1cblxuICAgICAgICAvLyBXaXRoaW4gc2FtZSBwcmlvcml0eSwgc29ydCBieSBkYXRlXG4gICAgICAgIGNvbnN0IGRhdGVBID0gbmV3IERhdGUoYS5kdWVEYXRlKS5nZXRUaW1lKClcbiAgICAgICAgY29uc3QgZGF0ZUIgPSBuZXcgRGF0ZShiLmR1ZURhdGUpLmdldFRpbWUoKVxuXG4gICAgICAgIGlmIChhLmNhdGVnb3J5ID09PSAnb3ZlcmR1ZScpIHtcbiAgICAgICAgICAgIC8vIEZvciBvdmVyZHVlOiBtb3N0IG92ZXJkdWUgZmlyc3QgKGVhcmxpZXN0IGR1ZSBkYXRlIGZpcnN0KVxuICAgICAgICAgICAgcmV0dXJuIGRhdGVBIC0gZGF0ZUJcbiAgICAgICAgfSBlbHNlIGlmIChhLmNhdGVnb3J5ID09PSAndXBjb21pbmcnKSB7XG4gICAgICAgICAgICAvLyBGb3IgdXBjb21pbmc6IHNvb25lc3QgZHVlIGRhdGUgZmlyc3RcbiAgICAgICAgICAgIHJldHVybiBkYXRlQSAtIGRhdGVCXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAvLyBGb3IgY29tcGxldGVkOiBtb3N0IHJlY2VudCBjb21wbGV0aW9uIGZpcnN0IChsYXRlc3QgZGF0ZSBmaXJzdClcbiAgICAgICAgICAgIHJldHVybiBkYXRlQiAtIGRhdGVBXG4gICAgICAgIH1cbiAgICB9KVxufVxuXG4vKipcbiAqIE1haW4gZnVuY3Rpb24gdG8gbWVyZ2UgYW5kIHNvcnQgY3JldyB0cmFpbmluZyBkYXRhIGZyb20gbXVsdGlwbGUgc291cmNlc1xuICogQHBhcmFtIG9wdGlvbnMgLSBDb25maWd1cmF0aW9uIG9iamVjdCB3aXRoIGRhdGEgc291cmNlcyBhbmQgdXRpbGl0aWVzXG4gKiBAcmV0dXJucyBVbmlmaWVkIGFuZCBzb3J0ZWQgdHJhaW5pbmcgZGF0YSBhcnJheVxuICovXG5leHBvcnQgY29uc3QgbWVyZ2VBbmRTb3J0Q3Jld1RyYWluaW5nRGF0YSA9ICh7XG4gICAgdHJhaW5pbmdTZXNzaW9uRHVlcyA9IFtdLFxuICAgIGNvbXBsZXRlZFRyYWluaW5nTGlzdCA9IFtdLFxuICAgIGdldFZlc3NlbFdpdGhJY29uLFxuICAgIGluY2x1ZGVDb21wbGV0ZWQgPSB0cnVlLFxuICAgIGRlYnVnID0gZmFsc2Vcbn06IHtcbiAgICB0cmFpbmluZ1Nlc3Npb25EdWVzPzogYW55W11cbiAgICBjb21wbGV0ZWRUcmFpbmluZ0xpc3Q/OiBhbnlbXVxuICAgIGdldFZlc3NlbFdpdGhJY29uPzogKGlkOiBhbnksIHZlc3NlbDogYW55KSA9PiBhbnlcbiAgICBpbmNsdWRlQ29tcGxldGVkPzogYm9vbGVhblxuICAgIGRlYnVnPzogYm9vbGVhblxufSk6IFVuaWZpZWRUcmFpbmluZ0RhdGFbXSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgLy8gRGVidWcgbG9nZ2luZyByZW1vdmVkIGZvciBwZXJmb3JtYW5jZVxuXG4gICAgICAgIC8vIFRyYW5zZm9ybSBvdmVyZHVlL3VwY29taW5nIHRyYWluaW5nIGRhdGFcbiAgICAgICAgY29uc3QgdHJhbnNmb3JtZWREdWVzID0gdHJhbnNmb3JtVHJhaW5pbmdTZXNzaW9uRHVlc1RvVW5pZmllZEZvcm1hdCh0cmFpbmluZ1Nlc3Npb25EdWVzKVxuXG4gICAgICAgIC8vIFRyYW5zZm9ybSBjb21wbGV0ZWQgdHJhaW5pbmcgZGF0YSBpZiByZXF1ZXN0ZWRcbiAgICAgICAgY29uc3QgdHJhbnNmb3JtZWRDb21wbGV0ZWQgPSBpbmNsdWRlQ29tcGxldGVkXG4gICAgICAgICAgICA/IHRyYW5zZm9ybUNvbXBsZXRlZFRyYWluaW5nVG9VbmlmaWVkRm9ybWF0KGNvbXBsZXRlZFRyYWluaW5nTGlzdCwgZ2V0VmVzc2VsV2l0aEljb24pXG4gICAgICAgICAgICA6IFtdXG5cbiAgICAgICAgLy8gQ29tYmluZSBhbGwgZGF0YVxuICAgICAgICBjb25zdCBjb21iaW5lZERhdGEgPSBbLi4udHJhbnNmb3JtZWREdWVzLCAuLi50cmFuc2Zvcm1lZENvbXBsZXRlZF1cblxuICAgICAgICAvLyBEZWJ1ZyBsb2dnaW5nIHJlbW92ZWQgZm9yIHBlcmZvcm1hbmNlXG5cbiAgICAgICAgLy8gUmVtb3ZlIGR1cGxpY2F0ZXMgYW5kIHNvcnQgd2l0aCBwcmlvcml0eS1iYXNlZCBvcmRlcmluZ1xuICAgICAgICBjb25zdCBkZWR1cGxpY2F0ZWREYXRhID0gZGVkdXBsaWNhdGVUcmFpbmluZ0RhdGEoY29tYmluZWREYXRhKVxuICAgICAgICBjb25zdCBzb3J0ZWREYXRhID0gc29ydFVuaWZpZWRUcmFpbmluZ0RhdGEoZGVkdXBsaWNhdGVkRGF0YSlcblxuICAgICAgICAvLyBEZWJ1ZyBsb2dnaW5nIHJlbW92ZWQgZm9yIHBlcmZvcm1hbmNlXG5cbiAgICAgICAgLy8gT3B0aW9uYWwgZGVidWcgYW5hbHlzaXNcbiAgICAgICAgaWYgKGRlYnVnKSB7XG4gICAgICAgICAgICBkZWJ1Z1RyYWluaW5nRGF0YShzb3J0ZWREYXRhLCAnRmluYWwgTWVyZ2VkIFRyYWluaW5nIERhdGEnKVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHNvcnRlZERhdGFcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgbWVyZ2luZyBhbmQgc29ydGluZyBjcmV3IHRyYWluaW5nIGRhdGE6JywgZXJyb3IpXG4gICAgICAgIHJldHVybiBbXVxuICAgIH1cbn1cblxuLyoqXG4gKiBGaWx0ZXIgdW5pZmllZCB0cmFpbmluZyBkYXRhIGJ5IGNhdGVnb3J5XG4gKiBAcGFyYW0gZGF0YSAtIFVuaWZpZWQgdHJhaW5pbmcgZGF0YSBhcnJheVxuICogQHBhcmFtIGNhdGVnb3JpZXMgLSBDYXRlZ29yaWVzIHRvIGluY2x1ZGVcbiAqIEByZXR1cm5zIEZpbHRlcmVkIGRhdGEgYXJyYXlcbiAqL1xuZXhwb3J0IGNvbnN0IGZpbHRlclRyYWluaW5nRGF0YUJ5Q2F0ZWdvcnkgPSAoXG4gICAgZGF0YTogVW5pZmllZFRyYWluaW5nRGF0YVtdLFxuICAgIGNhdGVnb3JpZXM6IEFycmF5PCdvdmVyZHVlJyB8ICd1cGNvbWluZycgfCAnY29tcGxldGVkJz5cbik6IFVuaWZpZWRUcmFpbmluZ0RhdGFbXSA9PiB7XG4gICAgcmV0dXJuIGRhdGEuZmlsdGVyKGl0ZW0gPT4gY2F0ZWdvcmllcy5pbmNsdWRlcyhpdGVtLmNhdGVnb3J5KSlcbn1cblxuLyoqXG4gKiBHZXQgdHJhaW5pbmcgZGF0YSBzdGF0aXN0aWNzXG4gKiBAcGFyYW0gZGF0YSAtIFVuaWZpZWQgdHJhaW5pbmcgZGF0YSBhcnJheVxuICogQHJldHVybnMgU3RhdGlzdGljcyBvYmplY3Qgd2l0aCBjb3VudHMgYnkgY2F0ZWdvcnlcbiAqL1xuZXhwb3J0IGNvbnN0IGdldFRyYWluaW5nRGF0YVN0YXRzID0gKGRhdGE6IFVuaWZpZWRUcmFpbmluZ0RhdGFbXSkgPT4ge1xuICAgIHJldHVybiB7XG4gICAgICAgIHRvdGFsOiBkYXRhLmxlbmd0aCxcbiAgICAgICAgb3ZlcmR1ZTogZGF0YS5maWx0ZXIoaXRlbSA9PiBpdGVtLmNhdGVnb3J5ID09PSAnb3ZlcmR1ZScpLmxlbmd0aCxcbiAgICAgICAgdXBjb21pbmc6IGRhdGEuZmlsdGVyKGl0ZW0gPT4gaXRlbS5jYXRlZ29yeSA9PT0gJ3VwY29taW5nJykubGVuZ3RoLFxuICAgICAgICBjb21wbGV0ZWQ6IGRhdGEuZmlsdGVyKGl0ZW0gPT4gaXRlbS5jYXRlZ29yeSA9PT0gJ2NvbXBsZXRlZCcpLmxlbmd0aFxuICAgIH1cbn1cblxuLyoqXG4gKiBEZWJ1ZyBmdW5jdGlvbiB0byBhbmFseXplIHRyYWluaW5nIGRhdGEgZm9yIGR1cGxpY2F0ZXMgYW5kIGlzc3Vlc1xuICogT25seSB1c2UgZm9yIGRlYnVnZ2luZyAtIG5vdCBmb3IgcHJvZHVjdGlvblxuICogQHBhcmFtIGRhdGEgLSBVbmlmaWVkIHRyYWluaW5nIGRhdGEgYXJyYXlcbiAqIEBwYXJhbSBsYWJlbCAtIExhYmVsIGZvciB0aGUgZGVidWcgb3V0cHV0XG4gKi9cbmV4cG9ydCBjb25zdCBkZWJ1Z1RyYWluaW5nRGF0YSA9IChkYXRhOiBVbmlmaWVkVHJhaW5pbmdEYXRhW10sIGxhYmVsOiBzdHJpbmcgPSAnVHJhaW5pbmcgRGF0YScpID0+IHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCcpIHJldHVyblxuXG4gICAgY29uc3QgaWRzID0gZGF0YS5tYXAoaXRlbSA9PiBpdGVtLmlkKVxuICAgIGNvbnN0IGR1cGxpY2F0ZUlkcyA9IGlkcy5maWx0ZXIoKGlkLCBpbmRleCkgPT4gaWRzLmluZGV4T2YoaWQpICE9PSBpbmRleClcblxuICAgIGNvbnNvbGUuZ3JvdXAoYPCflI0gJHtsYWJlbH0gQW5hbHlzaXNgKVxuICAgIGNvbnNvbGUubG9nKCdUb3RhbCByZWNvcmRzOicsIGRhdGEubGVuZ3RoKVxuICAgIGNvbnNvbGUubG9nKCdDYXRlZ29yaWVzOicsIGRhdGEucmVkdWNlKChhY2MsIGl0ZW0pID0+IHtcbiAgICAgICAgYWNjW2l0ZW0uY2F0ZWdvcnldID0gKGFjY1tpdGVtLmNhdGVnb3J5XSB8fCAwKSArIDFcbiAgICAgICAgcmV0dXJuIGFjY1xuICAgIH0sIHt9IGFzIFJlY29yZDxzdHJpbmcsIG51bWJlcj4pKVxuXG4gICAgaWYgKGR1cGxpY2F0ZUlkcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIER1cGxpY2F0ZSBJRHMgZm91bmQ6JywgQXJyYXkuZnJvbShuZXcgU2V0KGR1cGxpY2F0ZUlkcykpKVxuICAgICAgICBjb25zb2xlLmxvZygnRHVwbGljYXRlIHJlY29yZHM6JywgZGF0YS5maWx0ZXIoaXRlbSA9PiBkdXBsaWNhdGVJZHMuaW5jbHVkZXMoaXRlbS5pZCkpKVxuICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgTm8gZHVwbGljYXRlcyBmb3VuZCcpXG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ1NhbXBsZSByZWNvcmRzIGJ5IGNhdGVnb3J5OicpXG4gICAgY29uc3QgY2F0ZWdvcmllcyA9IFsnb3ZlcmR1ZScsICd1cGNvbWluZycsICdjb21wbGV0ZWQnXSBhcyBjb25zdFxuICAgIGNhdGVnb3JpZXMuZm9yRWFjaChjYXRlZ29yeSA9PiB7XG4gICAgICAgIGNvbnN0IHNhbXBsZSA9IGRhdGEuZmluZChpdGVtID0+IGl0ZW0uY2F0ZWdvcnkgPT09IGNhdGVnb3J5KVxuICAgICAgICBpZiAoc2FtcGxlKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgJHtjYXRlZ29yeX06YCwge1xuICAgICAgICAgICAgICAgIGlkOiBzYW1wbGUuaWQsXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiBzYW1wbGUudHJhaW5pbmdUeXBlPy50aXRsZSxcbiAgICAgICAgICAgICAgICBkdWVEYXRlOiBzYW1wbGUuZHVlRGF0ZSxcbiAgICAgICAgICAgICAgICB2ZXNzZWw6IHNhbXBsZS52ZXNzZWw/LnRpdGxlLFxuICAgICAgICAgICAgICAgIG1lbWJlcnNDb3VudDogc2FtcGxlLm1lbWJlcnM/Lmxlbmd0aCB8fCAwXG4gICAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgfSlcbiAgICBjb25zb2xlLmdyb3VwRW5kKClcbn1cbiJdLCJuYW1lcyI6WyJHZXRUcmFpbmluZ1Nlc3Npb25TdGF0dXMiLCJUcmFpbmluZ1ByaW9yaXR5IiwidHJhbnNmb3JtQ29tcGxldGVkVHJhaW5pbmdUb1VuaWZpZWRGb3JtYXQiLCJ0cmFpbmluZ0xpc3QiLCJnZXRWZXNzZWxXaXRoSWNvbiIsIkFycmF5IiwiaXNBcnJheSIsImNvbnNvbGUiLCJsb2ciLCJ0b3RhbFJlY29yZHMiLCJsZW5ndGgiLCJzYW1wbGVSZWNvcmQiLCJtYXAiLCJ0cmFpbmluZyIsImNvbXBsZXRlVmVzc2VsRGF0YSIsInZlc3NlbCIsImlkIiwidGl0bGUiLCJwb3NpdGlvbiIsInRyYWluaW5nTG9jYXRpb25UeXBlIiwiZXJyb3IiLCJ3YXJuIiwicmF3TWVtYmVycyIsIm1lbWJlcnMiLCJub2RlcyIsImRlZHVwbGljYXRlZE1lbWJlcnMiLCJyZWR1Y2UiLCJhY2MiLCJtZW1iZXIiLCJleGlzdGluZ01lbWJlciIsImZpbmQiLCJtIiwiZmlyc3ROYW1lIiwic3VybmFtZSIsImVtYWlsIiwicHVzaCIsImR1ZURhdGUiLCJkYXRlIiwidmVzc2VsSUQiLCJ0cmFpbmluZ1R5cGVJRCIsInRyYWluaW5nVHlwZXMiLCJ0cmFpbmluZ1R5cGUiLCJzdGF0dXMiLCJsYWJlbCIsImlzT3ZlcmR1ZSIsImNsYXNzIiwiZHVlV2l0aGluU2V2ZW5EYXlzIiwiY2F0ZWdvcnkiLCJvcmlnaW5hbERhdGEiLCJ0cmFuc2Zvcm1UcmFpbmluZ1Nlc3Npb25EdWVzVG9VbmlmaWVkRm9ybWF0IiwidHJhaW5pbmdTZXNzaW9uRHVlcyIsImZpbHRlcmVkRGF0YSIsImZpbHRlciIsIml0ZW0iLCJzZWFMb2dzTWVtYmVycyIsInNvbWUiLCJtZW1iZXJJRCIsIm9yaWdpbmFsQ291bnQiLCJmaWx0ZXJlZENvdW50IiwicmVtb3ZlZENvdW50IiwiZHVlV2l0aFN0YXR1cyIsImR1ZSIsInN0YXR1c0JyZWFrZG93biIsImtleSIsImdyb3VwZWREdWVzIiwidHJhaW5pbmdTZXNzaW9uIiwiZ3JvdXBDb3VudCIsIk9iamVjdCIsImtleXMiLCJncm91cEtleXMiLCJtZXJnZWREdWVzIiwidmFsdWVzIiwiZ3JvdXAiLCJtZXJnZWRNZW1iZXJzIiwiZW5oYW5jZWRWZXNzZWwiLCJjYXRlZ29yeUJyZWFrZG93biIsImdldFRyYWluaW5nUHJpb3JpdHkiLCJkZWR1cGxpY2F0ZVRyYWluaW5nRGF0YSIsImRhdGEiLCJzZWVuIiwiU2V0IiwiaGFzIiwiYWRkIiwic29ydFVuaWZpZWRUcmFpbmluZ0RhdGEiLCJzb3J0IiwiYSIsImIiLCJwcmlvcml0eURpZmYiLCJkYXRlQSIsIkRhdGUiLCJnZXRUaW1lIiwiZGF0ZUIiLCJtZXJnZUFuZFNvcnRDcmV3VHJhaW5pbmdEYXRhIiwiY29tcGxldGVkVHJhaW5pbmdMaXN0IiwiaW5jbHVkZUNvbXBsZXRlZCIsImRlYnVnIiwidHJhbnNmb3JtZWREdWVzIiwidHJhbnNmb3JtZWRDb21wbGV0ZWQiLCJjb21iaW5lZERhdGEiLCJkZWR1cGxpY2F0ZWREYXRhIiwic29ydGVkRGF0YSIsImRlYnVnVHJhaW5pbmdEYXRhIiwiZmlsdGVyVHJhaW5pbmdEYXRhQnlDYXRlZ29yeSIsImNhdGVnb3JpZXMiLCJpbmNsdWRlcyIsImdldFRyYWluaW5nRGF0YVN0YXRzIiwidG90YWwiLCJvdmVyZHVlIiwidXBjb21pbmciLCJjb21wbGV0ZWQiLCJwcm9jZXNzIiwiaWRzIiwiZHVwbGljYXRlSWRzIiwiaW5kZXgiLCJpbmRleE9mIiwiZnJvbSIsImZvckVhY2giLCJzYW1wbGUiLCJtZW1iZXJzQ291bnQiLCJncm91cEVuZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/crew-training-utils.ts\n"));

/***/ })

});
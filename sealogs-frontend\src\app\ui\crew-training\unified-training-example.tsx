'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { useLazyQuery } from '@apollo/client'
import {
    TRAINING_SESSIONS,
    READ_TRAINING_SESSION_DUES,
} from '@/app/lib/graphQL/query'
import { UnifiedTrainingTable } from './unified-training-table'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { Card } from '@/components/ui'
import { H2 } from '@/components/ui/typography'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { getTrainingDataStats } from '@/app/lib/crew-training-utils'
import { mergeAndSortCrewTrainingData } from '@/app/lib/crew-training-utils'
import { TrainingListFilter } from '@/components/filter'
import { useTrainingFilters } from './hooks/useTrainingFilters'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { Loader2 } from 'lucide-react'

interface UnifiedTrainingExampleProps {
    vesselId?: number
    memberId?: number
    isVesselView?: boolean
    excludeFilters?: string[]
}

export const UnifiedTrainingExample = ({
    vesselId,
    memberId,
    isVesselView = false,
    excludeFilters = [],
}: UnifiedTrainingExampleProps) => {
    const [trainingSessionDues, setTrainingSessionDues] = useState<any[]>([])
    const [completedTrainingList, setCompletedTrainingList] = useState<any[]>(
        [],
    )
    const [includeCompleted, setIncludeCompleted] = useState(true)
    const [loading, setLoading] = useState(false)

    // Filter options state
    const [vesselIdOptions, setVesselIdOptions] = useState<any[]>([])
    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = useState<any[]>(
        [],
    )
    const [trainerIdOptions, setTrainerIdOptions] = useState<any[]>([])
    const [crewIdOptions, setCrewIdOptions] = useState<any[]>([])
    const [page, setPage] = useState(0)
    const [permissions, setPermissions] = useState<any>(false)

    const { getVesselWithIcon } = useVesselIconData()

    // Initialize permissions
    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    // Query for training session dues (overdue/upcoming)
    const [queryTrainingSessionDues, { loading: duesLoading }] = useLazyQuery(
        READ_TRAINING_SESSION_DUES,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessionDues.nodes || []

                console.log(
                    '🔍 [UnifiedTrainingExample] Raw training session dues data:',
                    {
                        totalRecords: data.length,
                        sampleRecord: data[0],
                        allRecords: data,
                    },
                )

                setTrainingSessionDues(data)
            },
            onError: (error: any) => {
                console.error('Error loading training session dues:', error)
            },
        },
    )

    // Query for completed training sessions
    const [queryCompletedTraining, { loading: completedLoading }] =
        useLazyQuery(TRAINING_SESSIONS, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessions.nodes || []

                console.log(
                    '🔍 [UnifiedTrainingExample] Raw completed training sessions data:',
                    {
                        totalRecords: data.length,
                        sampleRecord: data[0],
                        allRecords: data,
                    },
                )

                // Extract filter options from completed training data
                const vesselIDs = Array.from(
                    new Set(data.map((item: any) => item.vessel.id)),
                ).filter((id: any) => +id !== 0)
                const trainingTypeIDs = Array.from(
                    new Set(
                        data.flatMap((item: any) =>
                            item.trainingTypes.nodes.map((t: any) => t.id),
                        ),
                    ),
                )
                const trainerIDs = Array.from(
                    new Set(data.map((item: any) => item.trainerID)),
                ).filter((id: any) => +id !== 0)
                const memberIDs = Array.from(
                    new Set(
                        data.flatMap((item: any) =>
                            item.members.nodes.map((t: any) => t.id),
                        ),
                    ),
                )

                setCompletedTrainingList(data)
                setVesselIdOptions(vesselIDs)
                setTrainingTypeIdOptions(trainingTypeIDs)
                setTrainerIdOptions(trainerIDs)
                setCrewIdOptions(memberIDs)
            },
            onError: (error: any) => {
                console.error('Error loading completed training:', error)
            },
        })

    // Load training session dues function
    const loadTrainingSessionDues = useCallback(
        async (filter: any) => {
            const duesFilter: any = {}
            if (memberId && memberId > 0) {
                duesFilter.memberID = { eq: +memberId }
            }
            if (vesselId && vesselId > 0) {
                duesFilter.vesselID = { eq: +vesselId }
            }
            if (filter.vesselID) {
                duesFilter.vesselID = filter.vesselID
            }
            if (filter.trainingTypes) {
                duesFilter.trainingTypeID = {
                    eq: filter.trainingTypes.id.contains,
                }
            }
            if (filter.members) {
                duesFilter.memberID = { eq: filter.members.id.contains }
            }
            if (filter.date) {
                duesFilter.dueDate = filter.date
            } else {
                duesFilter.dueDate = { ne: null }
            }

            await queryTrainingSessionDues({
                variables: {
                    filter: duesFilter,
                },
            })
        },
        [memberId, vesselId, queryTrainingSessionDues],
    )

    // Load completed training function
    const loadTrainingList = useCallback(
        async (startPage: number = 0, searchFilter: any = {}) => {
            const completedFilter: any = {}
            if (vesselId && vesselId > 0) {
                completedFilter.vesselID = { eq: +vesselId }
            }
            if (searchFilter.vesselID) {
                completedFilter.vesselID = searchFilter.vesselID
            }

            await queryCompletedTraining({
                variables: {
                    filter: completedFilter,
                    offset: startPage * 20, // Use 20 items per page to match pageSize
                    limit: 20,
                },
            })
        },
        [vesselId, queryCompletedTraining],
    )

    // Use training filters hook
    const { filter, setFilter, handleFilterChange } = useTrainingFilters({
        initialFilter: {},
        loadList: loadTrainingList,
        loadDues: loadTrainingSessionDues,
        toggleOverdue: () => {}, // Not used in unified view
    })

    // Memoize the load data function to prevent unnecessary re-renders
    const loadData = useCallback(async () => {
        setLoading(true)
        await loadTrainingSessionDues(filter)
        await loadTrainingList(page, filter)
        setLoading(false)
    }, [filter, page, loadTrainingSessionDues, loadTrainingList])

    // Load data on component mount
    useEffect(() => {
        const f: { members?: any } = { ...filter }
        if (memberId && +memberId > 0) {
            f.members = { id: { contains: +memberId } }
        }
        setFilter(f)
        loadData()
    }, []) // Only run on mount

    // Memoize the unified data calculation to prevent unnecessary recalculations
    const unifiedData = useMemo(() => {
        console.log(
            '🔍 [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:',
            {
                trainingSessionDuesCount: trainingSessionDues?.length || 0,
                completedTrainingListCount: completedTrainingList?.length || 0,
                includeCompleted,
                sampleDue: trainingSessionDues?.[0],
                sampleCompleted: completedTrainingList?.[0],
            },
        )

        const result = mergeAndSortCrewTrainingData({
            trainingSessionDues,
            completedTrainingList,
            getVesselWithIcon,
            includeCompleted,
        })

        console.log(
            '🔍 [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:',
            {
                totalRecords: result.length,
                categoryBreakdown: result.reduce((acc: any, item: any) => {
                    acc[item.category] = (acc[item.category] || 0) + 1
                    return acc
                }, {}),
                sampleRecord: result[0],
                allRecords: result,
            },
        )

        return result
    }, [
        trainingSessionDues,
        completedTrainingList,
        getVesselWithIcon,
        includeCompleted,
    ])

    const stats = useMemo(() => {
        return getTrainingDataStats(unifiedData)
    }, [unifiedData])

    // Enhanced loading state management
    const isLoading = loading || duesLoading || completedLoading
    const hasOverdueUpcomingData =
        trainingSessionDues && trainingSessionDues.length > 0
    const hasCompletedData =
        completedTrainingList && completedTrainingList.length > 0

    // Create detailed loading status
    const getLoadingStatus = () => {
        const statuses = []
        if (duesLoading) statuses.push('overdue/upcoming training')
        if (completedLoading) statuses.push('completed training')
        if (loading) statuses.push('training data')
        return statuses
    }

    // Comprehensive loading component
    const LoadingIndicator = ({ status }: { status: string[] }) => (
        <div className="flex items-center justify-center py-8">
            <div className="text-center space-y-3">
                <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                <div className="space-y-1">
                    <p className="text-sm font-medium">
                        Loading training data...
                    </p>
                    {status.length > 0 && (
                        <p className="text-xs text-muted-foreground">
                            Currently loading: {status.join(', ')}
                        </p>
                    )}
                </div>
            </div>
        </div>
    )

    // Partial loading component for when some data is available
    const PartialLoadingIndicator = ({ status }: { status: string[] }) => (
        <div className="flex items-center justify-center py-4 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Loading {status.join(', ')}...</span>
            </div>
        </div>
    )

    // Check permissions
    if (
        !permissions ||
        (!hasPermission('EDIT_TRAINING', permissions) &&
            !hasPermission('VIEW_TRAINING', permissions) &&
            !hasPermission('RECORD_TRAINING', permissions) &&
            !hasPermission('VIEW_MEMBER_TRAINING', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    return (
        <div className="w-full space-y-6">
            {/* Filter Section */}
            <Card>
                <TrainingListFilter
                    memberId={memberId}
                    onChange={handleFilterChange}
                    vesselIdOptions={vesselIdOptions}
                    trainingTypeIdOptions={trainingTypeIdOptions}
                    trainerIdOptions={trainerIdOptions}
                    memberIdOptions={crewIdOptions}
                    excludeFilters={excludeFilters}
                />
            </Card>

            <Card className="p-6">
                <div className="space-y-6">
                    {/* Header with statistics */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <H2>Crew Training Overview</H2>
                            <p className="text-sm text-muted-foreground mt-1">
                                Unified view of all training activities
                            </p>
                        </div>

                        <div className="flex flex-wrap gap-2">
                            <Badge
                                variant="destructive"
                                type="normal"
                                className="flex items-center gap-1">
                                <span className="w-2 h-2 bg-current rounded-full"></span>
                                Overdue: {stats.overdue}
                            </Badge>
                            <Badge
                                variant="secondary"
                                type="normal"
                                className="flex items-center gap-1">
                                <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                                Upcoming: {stats.upcoming}
                            </Badge>
                            <Badge
                                variant="outline"
                                type="normal"
                                className="flex items-center gap-1">
                                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                                Completed: {stats.completed}
                            </Badge>
                            <Badge variant="outline" type="normal">
                                Total: {stats.total}
                            </Badge>
                        </div>
                    </div>

                    {/* Filter controls */}
                    <div className="flex flex-wrap gap-2">
                        <Button
                            size="sm"
                            onClick={() =>
                                setIncludeCompleted(!includeCompleted)
                            }>
                            {includeCompleted ? 'Hide' : 'Show'} Completed
                        </Button>

                        <Button
                            variant="outline"
                            size="sm"
                            onClick={loadData}
                            disabled={isLoading}>
                            {isLoading ? 'Loading...' : 'Refresh'}
                        </Button>
                    </div>

                    {/* Unified training table with enhanced loading states */}
                    <div>
                        {isLoading &&
                        !hasOverdueUpcomingData &&
                        !hasCompletedData ? (
                            // Full loading state when no data is available
                            <LoadingIndicator status={getLoadingStatus()} />
                        ) : isLoading &&
                          (hasOverdueUpcomingData || hasCompletedData) ? (
                            // Partial loading state when some data is available
                            <div className="space-y-4">
                                <UnifiedTrainingTable
                                    trainingSessionDues={trainingSessionDues}
                                    completedTrainingList={
                                        completedTrainingList
                                    }
                                    getVesselWithIcon={getVesselWithIcon}
                                    includeCompleted={includeCompleted}
                                    memberId={memberId}
                                    isVesselView={isVesselView}
                                    showToolbar={false}
                                    pageSize={20}
                                />
                                <PartialLoadingIndicator
                                    status={getLoadingStatus()}
                                />
                            </div>
                        ) : (
                            // Normal state with data
                            <UnifiedTrainingTable
                                trainingSessionDues={trainingSessionDues}
                                completedTrainingList={completedTrainingList}
                                getVesselWithIcon={getVesselWithIcon}
                                includeCompleted={includeCompleted}
                                memberId={memberId}
                                isVesselView={isVesselView}
                                showToolbar={false}
                                pageSize={20}
                            />
                        )}
                    </div>

                    {/* Enhanced summary information */}
                    {!isLoading && unifiedData.length > 0 && (
                        <div className="text-sm text-muted-foreground space-y-1">
                            <p>
                                Showing {unifiedData.length} training record
                                {unifiedData.length !== 1 ? 's' : ''}. Data is
                                sorted with overdue trainings first, then
                                upcoming, then completed.
                            </p>
                            <div className="flex flex-wrap gap-4 text-xs">
                                <span>
                                    Overdue/Upcoming:{' '}
                                    {trainingSessionDues?.length || 0}
                                </span>
                                <span>
                                    Completed:{' '}
                                    {completedTrainingList?.length || 0}
                                </span>
                                <span>
                                    Last updated:{' '}
                                    {new Date().toLocaleTimeString()}
                                </span>
                            </div>
                        </div>
                    )}

                    {/* Loading status information */}
                    {isLoading &&
                        (hasOverdueUpcomingData || hasCompletedData) && (
                            <div className="text-xs text-muted-foreground">
                                <p>
                                    Some data is still loading. The table will
                                    update automatically when new data becomes
                                    available.
                                </p>
                            </div>
                        )}

                    {/* No data state */}
                    {!isLoading && unifiedData.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                            <p className="text-sm">
                                No training data available
                            </p>
                            <p className="text-xs mt-1">
                                Try adjusting your filters or refresh the data
                            </p>
                        </div>
                    )}
                </div>
            </Card>
        </div>
    )
}

export default UnifiedTrainingExample

import { GetTrainingSessionStatus } from './actions'

/**
 * Unified training data interface that combines overdue, upcoming, and completed training data
 */
export interface UnifiedTrainingData {
    id: number
    dueDate: string // For overdue/upcoming this is the due date, for completed this is the completion date
    vesselID: number
    vessel: {
        id: number
        title: string
        [key: string]: any // Allow for additional vessel properties like position, etc.
    }
    trainingTypeID: number
    trainingType: {
        id: number
        title: string
    }
    members: Array<{
        id: number
        firstName?: string
        surname?: string
    }>
    status: {
        class: string
        label: string
        isOverdue: boolean
        dueWithinSevenDays: boolean
    }
    category: 'overdue' | 'upcoming' | 'completed' // Added to help with sorting and display
    originalData?: any // Store original data for reference if needed
}

/**
 * Training priority levels for sorting
 */
export enum TrainingPriority {
    OVERDUE = 1,
    UPCOMING = 2,
    COMPLETED = 3
}

/**
 * Transform completed training sessions to match the unified training data format
 * @param trainingList - Array of completed training sessions
 * @param getVesselWithIcon - Function to get complete vessel data with position/icon
 * @returns Array of transformed training data
 */
export const transformCompletedTrainingToUnifiedFormat = (
    trainingList: any[],
    getVesselWithIcon?: (id: any, vessel: any) => any
): UnifiedTrainingData[] => {
    if (!trainingList || !Array.isArray(trainingList)) {
        return []
    }

    console.log('🔍 [crew-training-utils] Transforming completed training data:', {
        totalRecords: trainingList.length,
        sampleRecord: trainingList[0]
    })

    return trainingList.map((training: any) => {
        // Enhanced vessel data transformation with position information
        let completeVesselData = training.vessel || { id: 0, title: 'Unknown' }

        if (getVesselWithIcon && training.vessel?.id) {
            try {
                // Get complete vessel data including position, icon, and other metadata
                completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel)

                // Ensure we preserve original vessel data if transformation fails
                if (!completeVesselData || typeof completeVesselData !== 'object') {
                    completeVesselData = training.vessel
                }

                // Add position information if available
                if (training.vessel.position && !completeVesselData.position) {
                    completeVesselData.position = training.vessel.position
                }

                // Add location type if available
                if (training.trainingLocationType && !completeVesselData.trainingLocationType) {
                    completeVesselData.trainingLocationType = training.trainingLocationType
                }
            } catch (error) {
                console.warn('Failed to enhance vessel data for training:', training.id, error)
                completeVesselData = training.vessel
            }
        }

        // Enhanced member deduplication and normalization
        const rawMembers = training.members?.nodes || []
        const deduplicatedMembers = rawMembers.reduce((acc: any[], member: any) => {
            // Check if member already exists in the accumulator
            const existingMember = acc.find(m => m.id === member.id)

            if (existingMember) {
                // Update existing member with more complete data
                existingMember.firstName = member.firstName || existingMember.firstName
                existingMember.surname = member.surname || existingMember.surname
                existingMember.email = member.email || existingMember.email
            } else {
                // Add new member with normalized data
                acc.push({
                    id: member.id,
                    firstName: member.firstName || '',
                    surname: member.surname || '',
                    email: member.email || '',
                    ...member // Preserve any additional member data
                })
            }
            return acc
        }, [])

        return {
            id: training.id,
            dueDate: training.date, // Map completion date to dueDate for unified sorting
            vesselID: training.vessel?.id || 0,
            vessel: completeVesselData,
            trainingTypeID: training.trainingTypes?.nodes?.[0]?.id || 0,
            trainingType: training.trainingTypes?.nodes?.[0] || { id: 0, title: 'Unknown' },
            members: deduplicatedMembers,
            status: {
                label: 'Completed',
                isOverdue: false,
                class: 'border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center',
                dueWithinSevenDays: false,
            },
            category: 'completed' as const,
            originalData: training
        }
    })
}

/**
 * Transform training session dues to unified format with calculated status
 * Applies the same grouping logic as CrewTrainingList
 * @param trainingSessionDues - Array of training session dues (overdue/upcoming)
 * @returns Array of transformed training data with calculated status
 */
export const transformTrainingSessionDuesToUnifiedFormat = (
    trainingSessionDues: any[]
): UnifiedTrainingData[] => {
    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {
        return []
    }

    console.log('🔍 [crew-training-utils] Raw training session dues input:', {
        totalRecords: trainingSessionDues.length,
        sampleRecord: trainingSessionDues[0]
    })

    // Apply the same filtering logic as CrewTrainingList
    // Filter out crew members who are no longer assigned to the vessel
    const filteredData = trainingSessionDues.filter((item: any) =>
        item.vessel?.seaLogsMembers?.nodes?.some((m: any) => {
            return m.id === item.memberID
        }),
    )

    console.log('🔍 [crew-training-utils] After vessel member filtering:', {
        originalCount: trainingSessionDues.length,
        filteredCount: filteredData.length,
        removedCount: trainingSessionDues.length - filteredData.length
    })

    // Add status to each record
    const dueWithStatus = filteredData.map((due: any) => {
        const status = GetTrainingSessionStatus(due)
        return { ...due, status }
    })

    console.log('🔍 [crew-training-utils] After status calculation:', {
        totalRecords: dueWithStatus.length,
        statusBreakdown: dueWithStatus.reduce((acc: any, item: any) => {
            const key = item.status.isOverdue ? 'overdue' :
                       item.status.dueWithinSevenDays ? 'upcoming' : 'future'
            acc[key] = (acc[key] || 0) + 1
            return acc
        }, {})
    })

    // Apply the same grouping logic as CrewTrainingList
    // Group by vessel-trainingType-dueDate
    const groupedDues = dueWithStatus.reduce(
        (acc: any, due: any) => {
            const key = `${due.vesselID}-${due.trainingTypeID}-${due.dueDate}`
            if (!acc[key]) {
                acc[key] = {
                    id: due.id,
                    vesselID: due.vesselID,
                    vessel: due.vessel,
                    trainingTypeID: due.trainingTypeID,
                    trainingType: due.trainingType,
                    dueDate: due.dueDate,
                    status: due.status,
                    trainingLocationType: due.trainingSession?.trainingLocationType,
                    members: [],
                }
            }
            acc[key].members.push(due.member)
            return acc
        },
        {},
    )

    console.log('🔍 [crew-training-utils] After grouping by vessel-trainingType-dueDate:', {
        groupCount: Object.keys(groupedDues).length,
        groupKeys: Object.keys(groupedDues)
    })

    // Merge members within each group (same as CrewTrainingList)
    const mergedDues = Object.values(groupedDues).map((group: any) => {
        const mergedMembers = group.members.reduce(
            (acc: any, member: any) => {
                const existingMember = acc.find(
                    (m: any) => m.id === member.id,
                )
                if (existingMember) {
                    // Update existing member with more complete data
                    existingMember.firstName = member.firstName || existingMember.firstName
                    existingMember.surname = member.surname || existingMember.surname
                    existingMember.email = member.email || existingMember.email
                } else {
                    // Add new member with normalized data
                    acc.push({
                        id: member.id,
                        firstName: member.firstName || '',
                        surname: member.surname || '',
                        email: member.email || '',
                        ...member // Preserve any additional member data
                    })
                }
                return acc
            },
            [],
        )

        // Determine category based on status
        let category: 'overdue' | 'upcoming' | 'completed'
        if (group.status.isOverdue) {
            category = 'overdue'
        } else if (group.status.dueWithinSevenDays) {
            category = 'upcoming'
        } else {
            category = 'upcoming' // Default for future due dates
        }

        // Enhanced vessel data with position information
        const enhancedVessel = group.vessel || { id: 0, title: 'Unknown' }

        // Add training location type if available
        if (group.trainingLocationType && !enhancedVessel.trainingLocationType) {
            enhancedVessel.trainingLocationType = group.trainingLocationType
        }

        return {
            id: group.id,
            dueDate: group.dueDate,
            vesselID: group.vesselID,
            vessel: enhancedVessel,
            trainingTypeID: group.trainingTypeID,
            trainingType: group.trainingType || { id: 0, title: 'Unknown' },
            members: mergedMembers,
            status: group.status,
            category,
            originalData: group
        }
    })

    console.log('🔍 [crew-training-utils] Final merged dues data:', {
        totalRecords: mergedDues.length,
        categoryBreakdown: mergedDues.reduce((acc: any, item: any) => {
            acc[item.category] = (acc[item.category] || 0) + 1
            return acc
        }, {}),
        sampleRecord: mergedDues[0]
    })

    return mergedDues
}

/**
 * Get priority value for sorting based on training category and status
 * @param training - Unified training data item
 * @returns Priority number (lower = higher priority)
 */
const getTrainingPriority = (training: UnifiedTrainingData): number => {
    switch (training.category) {
        case 'overdue':
            return TrainingPriority.OVERDUE
        case 'upcoming':
            return TrainingPriority.UPCOMING
        case 'completed':
            return TrainingPriority.COMPLETED
        default:
            return TrainingPriority.COMPLETED
    }
}

/**
 * Remove duplicate training records based on ID with enhanced deduplication logic
 * @param data - Array of unified training data
 * @returns Deduplicated array with merged member data
 */
export const deduplicateTrainingData = (data: UnifiedTrainingData[]): UnifiedTrainingData[] => {
    const seenIds = new Set<number>()
    const deduplicatedData: UnifiedTrainingData[] = []

    for (const item of data) {
        if (seenIds.has(item.id)) {
            // Find existing item and merge member data
            const existingIndex = deduplicatedData.findIndex(existing => existing.id === item.id)
            if (existingIndex !== -1) {
                const existing = deduplicatedData[existingIndex]

                // Merge members from both records
                const combinedMembers = [...(existing.members || []), ...(item.members || [])]
                const uniqueMembers = combinedMembers.reduce((acc: any[], member: any) => {
                    const existingMember = acc.find(m => m.id === member.id)
                    if (!existingMember) {
                        acc.push(member)
                    }
                    return acc
                }, [])

                // Update the existing record with merged data
                deduplicatedData[existingIndex] = {
                    ...existing,
                    members: uniqueMembers,
                    // Prefer more recent or complete data
                    vessel: item.vessel?.title ? item.vessel : existing.vessel,
                    trainingType: item.trainingType?.title ? item.trainingType : existing.trainingType
                }
            }
        } else {
            seenIds.add(item.id)
            deduplicatedData.push(item)
        }
    }

    return deduplicatedData
}

/**
 * Sort unified training data with priority-based ordering
 * @param data - Array of unified training data
 * @returns Sorted array with overdue first, then upcoming, then completed
 */
export const sortUnifiedTrainingData = (data: UnifiedTrainingData[]): UnifiedTrainingData[] => {
    return data.sort((a, b) => {
        // First sort by priority (overdue > upcoming > completed)
        const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b)
        if (priorityDiff !== 0) {
            return priorityDiff
        }

        // Within same priority, sort by date
        const dateA = new Date(a.dueDate).getTime()
        const dateB = new Date(b.dueDate).getTime()

        if (a.category === 'overdue') {
            // For overdue: most overdue first (earliest due date first)
            return dateA - dateB
        } else if (a.category === 'upcoming') {
            // For upcoming: soonest due date first
            return dateA - dateB
        } else {
            // For completed: most recent completion first (latest date first)
            return dateB - dateA
        }
    })
}

/**
 * Main function to merge and sort crew training data from multiple sources
 * @param options - Configuration object with data sources and utilities
 * @returns Unified and sorted training data array
 */
export const mergeAndSortCrewTrainingData = ({
    trainingSessionDues = [],
    completedTrainingList = [],
    getVesselWithIcon,
    includeCompleted = true,
    debug = false
}: {
    trainingSessionDues?: any[]
    completedTrainingList?: any[]
    getVesselWithIcon?: (id: any, vessel: any) => any
    includeCompleted?: boolean
    debug?: boolean
}): UnifiedTrainingData[] => {
    try {
        // Debug logging removed for performance

        // Transform overdue/upcoming training data
        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues)

        // Transform completed training data if requested
        const transformedCompleted = includeCompleted
            ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon)
            : []

        // Combine all data
        const combinedData = [...transformedDues, ...transformedCompleted]

        // Debug logging removed for performance

        // Remove duplicates and sort with priority-based ordering
        const deduplicatedData = deduplicateTrainingData(combinedData)
        const sortedData = sortUnifiedTrainingData(deduplicatedData)

        // Debug logging removed for performance

        // Optional debug analysis
        if (debug) {
            debugTrainingData(sortedData, 'Final Merged Training Data')
        }

        return sortedData
    } catch (error) {
        console.error('❌ Error merging and sorting crew training data:', error)
        return []
    }
}

/**
 * Filter unified training data by category
 * @param data - Unified training data array
 * @param categories - Categories to include
 * @returns Filtered data array
 */
export const filterTrainingDataByCategory = (
    data: UnifiedTrainingData[],
    categories: Array<'overdue' | 'upcoming' | 'completed'>
): UnifiedTrainingData[] => {
    return data.filter(item => categories.includes(item.category))
}

/**
 * Get training data statistics
 * @param data - Unified training data array
 * @returns Statistics object with counts by category
 */
export const getTrainingDataStats = (data: UnifiedTrainingData[]) => {
    return {
        total: data.length,
        overdue: data.filter(item => item.category === 'overdue').length,
        upcoming: data.filter(item => item.category === 'upcoming').length,
        completed: data.filter(item => item.category === 'completed').length
    }
}

/**
 * Debug function to analyze training data for duplicates and issues
 * Only use for debugging - not for production
 * @param data - Unified training data array
 * @param label - Label for the debug output
 */
export const debugTrainingData = (data: UnifiedTrainingData[], label: string = 'Training Data') => {
    if (process.env.NODE_ENV !== 'development') return

    const ids = data.map(item => item.id)
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index)

    console.group(`🔍 ${label} Analysis`)
    console.log('Total records:', data.length)
    console.log('Categories:', data.reduce((acc, item) => {
        acc[item.category] = (acc[item.category] || 0) + 1
        return acc
    }, {} as Record<string, number>))

    if (duplicateIds.length > 0) {
        console.warn('⚠️ Duplicate IDs found:', Array.from(new Set(duplicateIds)))
        console.log('Duplicate records:', data.filter(item => duplicateIds.includes(item.id)))
    } else {
        console.log('✅ No duplicates found')
    }

    console.log('Sample records by category:')
    const categories = ['overdue', 'upcoming', 'completed'] as const
    categories.forEach(category => {
        const sample = data.find(item => item.category === category)
        if (sample) {
            console.log(`${category}:`, {
                id: sample.id,
                trainingType: sample.trainingType?.title,
                dueDate: sample.dueDate,
                vessel: sample.vessel?.title,
                membersCount: sample.members?.length || 0
            })
        }
    })
    console.groupEnd()
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/crew-training-utils */ \"(app-pages-browser)/./src/app/lib/crew-training-utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId, memberId, isVesselView = false, excludeFilters = [] } = param;\n    _s();\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter options state\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            // Extract filter options from completed training data\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            setCompletedTrainingList(data);\n            setVesselIdOptions(vesselIDs);\n            setTrainingTypeIdOptions(trainingTypeIDs);\n            setTrainerIdOptions(trainerIDs);\n            setCrewIdOptions(memberIDs);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 100,\n                limit: 100\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Use training filters hook\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_10__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: ()=>{}\n    });\n    // Memoize the load data function to prevent unnecessary re-renders\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setLoading(true);\n        await loadTrainingSessionDues(filter);\n        await loadTrainingList(page, filter);\n        setLoading(false);\n    }, [\n        filter,\n        page,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, [\n        loadData\n    ]);\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.getTrainingDataStats)(unifiedData);\n    }, [\n        unifiedData\n    ]);\n    const isLoading = loading || duesLoading || completedLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                    children: \"Crew Training Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground mt-1\",\n                                    children: \"Unified view of all training activities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"destructive\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-current rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Overdue: \",\n                                        stats.overdue\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"secondary\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Upcoming: \",\n                                        stats.upcoming\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Completed: \",\n                                        stats.completed\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    type: \"normal\",\n                                    children: [\n                                        \"Total: \",\n                                        stats.total\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            size: \"sm\",\n                            onClick: ()=>setIncludeCompleted(!includeCompleted),\n                            children: [\n                                includeCompleted ? \"Hide\" : \"Show\",\n                                \" Completed\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: loadData,\n                            disabled: isLoading,\n                            children: isLoading ? \"Loading...\" : \"Refresh\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Loading training data...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                        trainingSessionDues: trainingSessionDues,\n                        completedTrainingList: completedTrainingList,\n                        getVesselWithIcon: getVesselWithIcon,\n                        includeCompleted: includeCompleted,\n                        memberId: memberId,\n                        isVesselView: isVesselView,\n                        showToolbar: false\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 17\n                }, undefined),\n                !isLoading && unifiedData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Showing \",\n                            unifiedData.length,\n                            \" training record\",\n                            unifiedData.length !== 1 ? \"s\" : \"\",\n                            \". Data is sorted with overdue trainings first, then upcoming, then completed.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 253,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 252,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"IZHHBFmTHfJAb1oRqWDAviFOIEc=\", false, function() {\n    return [\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_10__.useTrainingFilters\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});
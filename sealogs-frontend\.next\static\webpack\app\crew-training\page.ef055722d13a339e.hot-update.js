"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/crew-training-utils */ \"(app-pages-browser)/./src/app/lib/crew-training-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId, memberId, isVesselView = false, excludeFilters = [] } = param;\n    _s();\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter options state\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setCompletedTrainingList(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Memoize the load data function to prevent unnecessary re-renders\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setLoading(true);\n        // Build filters based on props\n        const duesFilter = {};\n        const completedFilter = {};\n        if (vesselId) {\n            duesFilter.vesselID = {\n                eq: vesselId\n            };\n            completedFilter.vesselID = {\n                eq: vesselId\n            };\n        }\n        if (memberId) {\n            duesFilter.memberID = {\n                eq: memberId\n            };\n        // For completed training, we'd need to filter by members in the training session\n        }\n        // Load training session dues\n        await queryTrainingSessionDues({\n            variables: {\n                limit: 1000,\n                offset: 0,\n                filter: duesFilter\n            }\n        });\n        // Load completed training sessions\n        await queryCompletedTraining({\n            variables: {\n                limit: 1000,\n                offset: 0,\n                filter: completedFilter\n            }\n        });\n        setLoading(false);\n    }, [\n        vesselId,\n        memberId,\n        queryTrainingSessionDues,\n        queryCompletedTraining\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, [\n        loadData\n    ]);\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.getTrainingDataStats)(unifiedData);\n    }, [\n        unifiedData\n    ]);\n    const isLoading = loading || duesLoading || completedLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                    children: \"Crew Training Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground mt-1\",\n                                    children: \"Unified view of all training activities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"destructive\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-current rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Overdue: \",\n                                        stats.overdue\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"secondary\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Upcoming: \",\n                                        stats.upcoming\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Completed: \",\n                                        stats.completed\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    type: \"normal\",\n                                    children: [\n                                        \"Total: \",\n                                        stats.total\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            size: \"sm\",\n                            onClick: ()=>setIncludeCompleted(!includeCompleted),\n                            children: [\n                                includeCompleted ? \"Hide\" : \"Show\",\n                                \" Completed\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: loadData,\n                            disabled: isLoading,\n                            children: isLoading ? \"Loading...\" : \"Refresh\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Loading training data...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                        trainingSessionDues: trainingSessionDues,\n                        completedTrainingList: completedTrainingList,\n                        getVesselWithIcon: getVesselWithIcon,\n                        includeCompleted: includeCompleted,\n                        memberId: memberId,\n                        isVesselView: isVesselView,\n                        showToolbar: false\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 17\n                }, undefined),\n                !isLoading && unifiedData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Showing \",\n                            unifiedData.length,\n                            \" training record\",\n                            unifiedData.length !== 1 ? \"s\" : \"\",\n                            \". Data is sorted with overdue trainings first, then upcoming, then completed.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 192,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 191,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"2CKiN0APyQtF31f10z/SzNAb8SE=\", false, function() {\n    return [\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});
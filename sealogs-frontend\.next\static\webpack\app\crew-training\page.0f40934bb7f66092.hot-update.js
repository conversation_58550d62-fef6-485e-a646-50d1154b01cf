"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/crew-training-utils */ \"(app-pages-browser)/./src/app/lib/crew-training-utils.ts\");\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId, memberId, isVesselView = false, excludeFilters = [] } = param;\n    _s();\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter options state\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.getPermissions);\n    }, []);\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            // Extract filter options from completed training data\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            setCompletedTrainingList(data);\n            setVesselIdOptions(vesselIDs);\n            setTrainingTypeIdOptions(trainingTypeIDs);\n            setTrainerIdOptions(trainerIDs);\n            setCrewIdOptions(memberIDs);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 20,\n                limit: 20\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Use training filters hook\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_11__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: ()=>{}\n    });\n    // Memoize the load data function to prevent unnecessary re-renders\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setLoading(true);\n        await loadTrainingSessionDues(filter);\n        await loadTrainingList(page, filter);\n        setLoading(false);\n    }, [\n        filter,\n        page,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const f = {\n            ...filter\n        };\n        if (memberId && +memberId > 0) {\n            f.members = {\n                id: {\n                    contains: +memberId\n                }\n            };\n        }\n        setFilter(f);\n        loadData();\n    }, []) // Only run on mount\n    ;\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.getTrainingDataStats)(unifiedData);\n    }, [\n        unifiedData\n    ]);\n    // Enhanced loading state management\n    const isLoading = loading || duesLoading || completedLoading;\n    const hasOverdueUpcomingData = trainingSessionDues && trainingSessionDues.length > 0;\n    const hasCompletedData = completedTrainingList && completedTrainingList.length > 0;\n    // Create detailed loading status\n    const getLoadingStatus = ()=>{\n        const statuses = [];\n        if (duesLoading) statuses.push(\"overdue/upcoming training\");\n        if (completedLoading) statuses.push(\"completed training\");\n        if (loading) statuses.push(\"training data\");\n        return statuses;\n    };\n    // Comprehensive loading component\n    const LoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Loading training data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 21\n                            }, undefined),\n                            status.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"Currently loading: \",\n                                    status.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 282,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 281,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Partial loading component for when some data is available\n    const PartialLoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-4 bg-muted/30 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Loading \",\n                            status.join(\", \"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 301,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 300,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Check permissions\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 317,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 319,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_10__.TrainingListFilter, {\n                    memberId: memberId,\n                    onChange: handleFilterChange,\n                    vesselIdOptions: vesselIdOptions,\n                    trainingTypeIdOptions: trainingTypeIdOptions,\n                    trainerIdOptions: trainerIdOptions,\n                    memberIdOptions: crewIdOptions,\n                    excludeFilters: excludeFilters\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 326,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                            children: \"Crew Training Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mt-1\",\n                                            children: \"Unified view of all training activities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"destructive\",\n                                            type: \"normal\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-current rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"Overdue: \",\n                                                stats.overdue\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"secondary\",\n                                            type: \"normal\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"Upcoming: \",\n                                                stats.upcoming\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"outline\",\n                                            type: \"normal\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"Completed: \",\n                                                stats.completed\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"outline\",\n                                            type: \"normal\",\n                                            children: [\n                                                \"Total: \",\n                                                stats.total\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    size: \"sm\",\n                                    onClick: ()=>setIncludeCompleted(!includeCompleted),\n                                    children: [\n                                        includeCompleted ? \"Hide\" : \"Show\",\n                                        \" Completed\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: loadData,\n                                    disabled: isLoading,\n                                    children: isLoading ? \"Loading...\" : \"Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: isLoading && !hasOverdueUpcomingData && !hasCompletedData ? // Full loading state when no data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingIndicator, {\n                                status: getLoadingStatus()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 29\n                            }, undefined) : isLoading && (hasOverdueUpcomingData || hasCompletedData) ? // Partial loading state when some data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                        trainingSessionDues: trainingSessionDues,\n                                        completedTrainingList: completedTrainingList,\n                                        getVesselWithIcon: getVesselWithIcon,\n                                        includeCompleted: includeCompleted,\n                                        memberId: memberId,\n                                        isVesselView: isVesselView,\n                                        showToolbar: false,\n                                        pageSize: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PartialLoadingIndicator, {\n                                        status: getLoadingStatus()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 29\n                            }, undefined) : // Normal state with data\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                trainingSessionDues: trainingSessionDues,\n                                completedTrainingList: completedTrainingList,\n                                getVesselWithIcon: getVesselWithIcon,\n                                includeCompleted: includeCompleted,\n                                memberId: memberId,\n                                isVesselView: isVesselView,\n                                showToolbar: false,\n                                pageSize: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 21\n                        }, undefined),\n                        !isLoading && unifiedData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Showing \",\n                                    unifiedData.length,\n                                    \" training record\",\n                                    unifiedData.length !== 1 ? \"s\" : \"\",\n                                    \". Data is sorted with overdue trainings first, then upcoming, then completed.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 338,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 324,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"mrgy7o+QorNtpBAb4HlJuOoHA7s=\", false, function() {\n    return [\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_11__.useTrainingFilters\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-table.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingTable: function() { return /* binding */ UnifiedTrainingTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/crew-training-utils */ \"(app-pages-browser)/./src/app/lib/crew-training-utils.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\nconst getRowStatus = (rowData)=>{\n    if (rowData.status.isOverdue) {\n        return \"overdue\";\n    }\n    if (rowData.status.dueWithinSevenDays) {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\nconst UnifiedMobileTrainingCard = (param)=>{\n    let { data, memberId } = param;\n    var _data_trainingType, _data_vessel;\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const isCompleted = data.category === \"completed\";\n    const isOverdue = data.category === \"overdue\";\n    const isUpcoming = data.category === \"upcoming\";\n    const members = data.members || [];\n    const trainingTitle = ((_data_trainingType = data.trainingType) === null || _data_trainingType === void 0 ? void 0 : _data_trainingType.title) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-3 tablet-md:border-none border-b border-border py-3 small:pe-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap justify-between items-center\",\n                children: [\n                    isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/crew-training/info?id=\".concat(data.id),\n                        className: \"font-semibold text-base hover:text-primary\",\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"font-semibold text-base\", isOverdue && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                        children: trainingTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 items-center landscape:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                className: \"text-sm m-0 text-muted-foreground\",\n                                children: \"Status:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-sm font-medium px-2 py-1 rounded-md\", data.status.isOverdue ? \"bg-destructive/10 text-destructive\" : data.status.dueWithinSevenDays ? \"bg-warning/10 text-warning\" : \"bg-muted/50 text-muted-foreground\"),\n                                children: data.status.label || data.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 81,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"tablet-md:hidden space-y-[7px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Training Details:\" : \"Due Date:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-sm\", isOverdue && \"text-cinnabar-500\"),\n                        children: isCompleted ? trainingTitle : data.dueDate ? formatDate(data.dueDate) : \"Not specified\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                position: \"left\",\n                className: \"text-sm laptop:hidden text-muted-foreground\",\n                label: isCompleted ? \"Team:\" : \"Crew Members:\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 flex-wrap\",\n                    children: [\n                        members.slice(0, bp.isTabletMd ? 8 : 6).map((member)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: !isCompleted && isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, member.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 29\n                            }, undefined);\n                        }),\n                        members.length > (bp.isTabletMd ? 8 : 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                                    className: \"w-fit\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-fit h-8\",\n                                        children: [\n                                            \"+\",\n                                            members.length - (bp.isTabletMd ? 8 : 6),\n                                            \" \",\n                                            \"more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                                    className: \"w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 max-h-64 overflow-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: members.slice(bp.isTabletMd ? 8 : 6).map((remainingMember)=>/*#__PURE__*/ {\n                                                var _remainingMember_firstName, _remainingMember_surname;\n                                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                            size: \"xs\",\n                                                            variant: \"secondary\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                className: \"text-xs\",\n                                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(remainingMember.firstName, remainingMember.surname)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        \"\".concat((_remainingMember_firstName = remainingMember.firstName) !== null && _remainingMember_firstName !== void 0 ? _remainingMember_firstName : \"\", \" \").concat((_remainingMember_surname = remainingMember.surname) !== null && _remainingMember_surname !== void 0 ? _remainingMember_surname : \"\")\n                                                    ]\n                                                }, remainingMember.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 49\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between landscape:hidden items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Vessel:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_data_vessel = data.vessel) === null || _data_vessel === void 0 ? void 0 : _data_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: data.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 201,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 80,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedMobileTrainingCard, \"ZxSHrfPd9jdclp97BkZrjvTJck4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints\n    ];\n});\n_c = UnifiedMobileTrainingCard;\nconst UnifiedTrainingTable = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, memberId, isVesselView = false, showToolbar = false, pageSize } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery)(\"(min-width: 720px)\");\n    // Use the utility function to merge and sort data\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Determine if we have mixed data types or primarily one type\n    const hasOverdueOrUpcoming = unifiedData.some((item)=>item.category === \"overdue\" || item.category === \"upcoming\");\n    const hasCompleted = unifiedData.some((item)=>item.category === \"completed\");\n    const isUnifiedView = hasOverdueOrUpcoming && hasCompleted;\n    // Create different column sets based on data type\n    const getColumnsForDataType = ()=>{\n        // Common mobile training card column\n        const mobileColumn = {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Training\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                    data: training,\n                    memberId: memberId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                // Sort by category priority first, then by date\n                const trainingA = rowA.original;\n                const trainingB = rowB.original;\n                const priorityA = trainingA.category === \"overdue\" ? 1 : trainingA.category === \"upcoming\" ? 2 : 3;\n                const priorityB = trainingB.category === \"overdue\" ? 1 : trainingB.category === \"upcoming\" ? 2 : 3;\n                if (priorityA !== priorityB) {\n                    return priorityA - priorityB;\n                }\n                const dateA = new Date(trainingA.dueDate).getTime();\n                const dateB = new Date(trainingB.dueDate).getTime();\n                return trainingA.category === \"completed\" ? dateB - dateA : dateA - dateB;\n            }\n        };\n        // If we have only completed training, use completed training columns\n        if (hasCompleted && !hasOverdueOrUpcoming) {\n            return [\n                mobileColumn,\n                {\n                    accessorKey: \"trainingDrillsCompleted\",\n                    cellAlignment: \"left\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Training/drills completed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"tablet-md\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_trainingType;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                            children: ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 32\n                        }, undefined);\n                    }\n                },\n                {\n                    accessorKey: \"where\",\n                    cellAlignment: \"left\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Where\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_vessel;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-nowrap\",\n                                    children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                    vessel: training.vessel,\n                                    iconClassName: \"size-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                },\n                {\n                    accessorKey: \"trainer\",\n                    cellAlignment: \"center\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Trainer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_originalData;\n                        const training = row.original;\n                        const trainer = (_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainer;\n                        if (!trainer) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 46\n                        }, undefined);\n                        var _trainer_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-nowrap\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            trainer.firstName,\n                                            \" \",\n                                            (_trainer_surname = trainer.surname) !== null && _trainer_surname !== void 0 ? _trainer_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                },\n                {\n                    accessorKey: \"who\",\n                    cellAlignment: \"right\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Who\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"laptop\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        const training = row.original;\n                        const members = training.members || [];\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex items-end gap-1\",\n                            children: members.map((member, index)=>/*#__PURE__*/ {\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                size: \"sm\",\n                                                variant: \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 37\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                }\n            ];\n        }\n        // If we have only overdue/upcoming training, use overdue training columns\n        if (hasOverdueOrUpcoming && !hasCompleted) {\n            return [\n                mobileColumn,\n                {\n                    accessorKey: \"vessel\",\n                    cellAlignment: \"left\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Vessel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_vessel;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: !isVesselView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 37\n                            }, undefined)\n                        }, void 0, false);\n                    }\n                },\n                {\n                    accessorKey: \"crew\",\n                    cellAlignment: \"right\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Crew\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"laptop\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_status;\n                        const training = row.original;\n                        const members = training.members || [];\n                        return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-1\",\n                            children: members.map((member)=>/*#__PURE__*/ {\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                size: \"sm\",\n                                                variant: training.status.isOverdue ? \"destructive\" : \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, member.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 37\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 29\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"!rounded-full size-10\", (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.class),\n                            children: members.length\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                },\n                {\n                    accessorKey: \"status\",\n                    cellAlignment: \"right\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_status, _training_status1, _training_status2;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? (_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                            children: ((_training_status2 = training.status) === null || _training_status2 === void 0 ? void 0 : _training_status2.label) || \"Unknown Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                }\n            ];\n        }\n        // Default unified view with all columns\n        return [\n            mobileColumn,\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_trainingType;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(training.status.isOverdue && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                        children: ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 25\n                    }, undefined);\n                }\n            },\n            {\n                accessorKey: \"dueDate\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-right\", training.status.isOverdue && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                        children: formatDate(training.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 25\n                    }, undefined);\n                }\n            },\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 25\n                    }, undefined);\n                }\n            }\n        ];\n    };\n    // Create table columns\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)(getColumnsForDataType()), [\n        memberId,\n        hasOverdueOrUpcoming,\n        hasCompleted,\n        isVesselView,\n        isWide\n    ]);\n    if (!(unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: \"No training data available\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n            lineNumber: 602,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.FilteredTable, {\n        columns: columns,\n        data: unifiedData,\n        showToolbar: showToolbar,\n        rowStatus: getRowStatus,\n        pageSize: pageSize || unifiedData.length\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 609,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(UnifiedTrainingTable, \"x7VOLZGRwr6UzHBlctBEB/GNMsI=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery\n    ];\n});\n_c1 = UnifiedTrainingTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingTable);\nvar _c, _c1;\n$RefreshReg$(_c, \"UnifiedMobileTrainingCard\");\n$RefreshReg$(_c1, \"UnifiedTrainingTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\n"));

/***/ })

});
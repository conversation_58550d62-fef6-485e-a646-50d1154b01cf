"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/lib/crew-training-utils.ts":
/*!********************************************!*\
  !*** ./src/app/lib/crew-training-utils.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Transforming completed training data:\", {\n        totalRecords: trainingList.length,\n        sampleRecord: trainingList[0]\n    });\n    return trainingList.map((training)=>{\n        var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n        // Enhanced vessel data transformation with position information\n        let completeVesselData = training.vessel || {\n            id: 0,\n            title: \"Unknown\"\n        };\n        if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n            try {\n                // Get complete vessel data including position, icon, and other metadata\n                completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                // Ensure we preserve original vessel data if transformation fails\n                if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                    completeVesselData = training.vessel;\n                }\n                // Add position information if available\n                if (training.vessel.position && !completeVesselData.position) {\n                    completeVesselData.position = training.vessel.position;\n                }\n                // Add location type if available\n                if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                    completeVesselData.trainingLocationType = training.trainingLocationType;\n                }\n            } catch (error) {\n                console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                completeVesselData = training.vessel;\n            }\n        }\n        // Enhanced member deduplication and normalization\n        const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n        const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n            // Check if member already exists in the accumulator\n            const existingMember = acc.find((m)=>m.id === member.id);\n            if (existingMember) {\n                // Update existing member with more complete data\n                existingMember.firstName = member.firstName || existingMember.firstName;\n                existingMember.surname = member.surname || existingMember.surname;\n                existingMember.email = member.email || existingMember.email;\n            } else {\n                // Add new member with normalized data\n                acc.push({\n                    id: member.id,\n                    firstName: member.firstName || \"\",\n                    surname: member.surname || \"\",\n                    email: member.email || \"\",\n                    ...member // Preserve any additional member data\n                });\n            }\n            return acc;\n        }, []);\n        return {\n            id: training.id,\n            dueDate: training.date,\n            vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n            vessel: completeVesselData,\n            trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n            trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: deduplicatedMembers,\n            status: {\n                label: \"Completed\",\n                isOverdue: false,\n                class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                dueWithinSevenDays: false\n            },\n            category: \"completed\",\n            originalData: training\n        };\n    });\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No training session dues provided\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Raw training session dues input:\", {\n        totalRecords: trainingSessionDues.length,\n        sampleRecord: trainingSessionDues[0]\n    });\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item but log it\n            if (!hasValidVesselMembers) {\n                var _item_vessel_seaLogsMembers_nodes1, _item_vessel_seaLogsMembers1, _item_vessel1;\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Item without vessel member match:\", {\n                    memberID: item.memberID,\n                    vesselID: item.vesselID,\n                    vesselMembers: (_item_vessel1 = item.vessel) === null || _item_vessel1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers1 = _item_vessel1.seaLogsMembers) === null || _item_vessel_seaLogsMembers1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes1 = _item_vessel_seaLogsMembers1.nodes) === null || _item_vessel_seaLogsMembers_nodes1 === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes1.map((m)=>m === null || m === void 0 ? void 0 : m.id)\n                });\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n            originalCount: trainingSessionDues.length,\n            filteredCount: filteredData.length,\n            removedCount: trainingSessionDues.length - filteredData.length\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n            totalRecords: dueWithStatus.length,\n            statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n                const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n                acc[key] = (acc[key] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n            groupCount: Object.keys(groupedDues).length,\n            groupKeys: Object.keys(groupedDues)\n        });\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            var _group_status, _group_status1;\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            group.members.forEach((member)=>{\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            // Determine category based on status\n            let category;\n            if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                category = \"overdue\";\n            } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                category = \"upcoming\";\n            } else {\n                category = \"upcoming\" // Default for future due dates\n                ;\n            }\n            // Enhanced vessel data with position information\n            const enhancedVessel = group.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            // Add training location type if available\n            if (group.trainingLocationType && !enhancedVessel.trainingLocationType) {\n                enhancedVessel.trainingLocationType = group.trainingLocationType;\n            }\n            return {\n                id: group.id,\n                dueDate: group.dueDate,\n                vesselID: group.vesselID,\n                vessel: enhancedVessel,\n                trainingTypeID: group.trainingTypeID,\n                trainingType: group.trainingType || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: mergedMembers,\n                status: group.status,\n                category,\n                originalData: group\n            };\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n            totalRecords: mergedDues.length,\n            categoryBreakdown: mergedDues.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: mergedDues[0]\n        });\n        return mergedDues;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * Optimized version using Map for better performance\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for deduplication\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting deduplication:\", {\n        totalRecords: data.length,\n        sampleRecord: data[0]\n    });\n    try {\n        // Use Map for O(1) lookups instead of O(n) findIndex\n        const recordMap = new Map();\n        for (const item of data){\n            if (!item || typeof item.id !== \"number\") {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item in deduplication:\", item);\n                continue;\n            }\n            if (recordMap.has(item.id)) {\n                var // Add existing members\n                _existing_members, // Add/merge new members\n                _item_members, _item_vessel, _item_trainingType, _item_status;\n                // Merge with existing record\n                const existing = recordMap.get(item.id);\n                // Use Map for member deduplication for better performance\n                const memberMap = new Map();\n                (_existing_members = existing.members) === null || _existing_members === void 0 ? void 0 : _existing_members.forEach((member)=>{\n                    if (member && member.id) {\n                        memberMap.set(member.id, member);\n                    }\n                });\n                (_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.forEach((member)=>{\n                    if (member && member.id) {\n                        const existingMember = memberMap.get(member.id);\n                        if (existingMember) {\n                            // Merge member data, preferring non-empty values\n                            memberMap.set(member.id, {\n                                ...existingMember,\n                                firstName: member.firstName || existingMember.firstName,\n                                surname: member.surname || existingMember.surname,\n                                email: member.email || existingMember.email,\n                                ...member // Preserve any additional member data\n                            });\n                        } else {\n                            memberMap.set(member.id, member);\n                        }\n                    }\n                });\n                // Update the existing record with merged data\n                recordMap.set(item.id, {\n                    ...existing,\n                    members: Array.from(memberMap.values()),\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) && item.vessel.title !== \"Unknown\" ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) && item.trainingType.title !== \"Unknown\" ? item.trainingType : existing.trainingType,\n                    // Prefer overdue status over upcoming/completed\n                    status: ((_item_status = item.status) === null || _item_status === void 0 ? void 0 : _item_status.isOverdue) ? item.status : existing.status\n                });\n            } else {\n                // Add new record\n                recordMap.set(item.id, item);\n            }\n        }\n        const deduplicatedData = Array.from(recordMap.values());\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Deduplication complete:\", {\n            originalCount: data.length,\n            deduplicatedCount: deduplicatedData.length,\n            removedDuplicates: data.length - deduplicatedData.length,\n            categoryBreakdown: deduplicatedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        return deduplicatedData;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in deduplication:\", error);\n        return data // Return original data if deduplication fails\n        ;\n    }\n};\n/**\n * Sort unified training data with priority-based ordering\n * Optimized version with better performance and error handling\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for sorting\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting sort:\", {\n        totalRecords: data.length,\n        categories: data.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    try {\n        return data.sort((a, b)=>{\n            try {\n                // First sort by priority (overdue > upcoming > completed)\n                const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n                if (priorityDiff !== 0) {\n                    return priorityDiff;\n                }\n                // Within same priority, sort by date with error handling\n                const dateA = a.dueDate ? new Date(a.dueDate).getTime() : 0;\n                const dateB = b.dueDate ? new Date(b.dueDate).getTime() : 0;\n                // Handle invalid dates\n                if (isNaN(dateA) && isNaN(dateB)) return 0;\n                if (isNaN(dateA)) return 1;\n                if (isNaN(dateB)) return -1;\n                if (a.category === \"overdue\") {\n                    // For overdue: most overdue first (earliest due date first)\n                    return dateA - dateB;\n                } else if (a.category === \"upcoming\") {\n                    // For upcoming: soonest due date first\n                    return dateA - dateB;\n                } else {\n                    // For completed: most recent completion first (latest date first)\n                    return dateB - dateA;\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error comparing items in sort:\", error, {\n                    a,\n                    b\n                });\n                return 0 // Keep original order if comparison fails\n                ;\n            }\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in sorting:\", error);\n        return data // Return original data if sorting fails\n        ;\n    }\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * Optimized version with comprehensive debugging and error handling\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting merge process:\", {\n        trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n        completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n        includeCompleted,\n        debug\n    });\n    try {\n        // Transform overdue/upcoming training data\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming training session dues...\");\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed dues result:\", {\n            count: transformedDues.length,\n            sample: transformedDues[0]\n        });\n        // Transform completed training data if requested\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming completed training...\");\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed completed result:\", {\n            count: transformedCompleted.length,\n            sample: transformedCompleted[0]\n        });\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Combined data:\", {\n            totalCount: combinedData.length,\n            duesCount: transformedDues.length,\n            completedCount: transformedCompleted.length,\n            sample: combinedData[0]\n        });\n        // Remove duplicates and sort with priority-based ordering\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting deduplication...\");\n        const deduplicatedData = deduplicateTrainingData(combinedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Deduplication complete:\", {\n            beforeCount: combinedData.length,\n            afterCount: deduplicatedData.length,\n            sample: deduplicatedData[0]\n        });\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting sorting...\");\n        const sortedData = sortUnifiedTrainingData(deduplicatedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Final result:\", {\n            totalCount: sortedData.length,\n            categoryBreakdown: sortedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sample: sortedData[0]\n        });\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        console.error(\"❌ Error stack:\", error instanceof Error ? error.stack : \"No stack trace\");\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/crew-training-utils.ts\n"));

/***/ })

});
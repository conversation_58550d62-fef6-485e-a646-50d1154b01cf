"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/lib/crew-training-utils.ts":
/*!********************************************!*\
  !*** ./src/app/lib/crew-training-utils.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Transforming completed training data:\", {\n        totalRecords: trainingList.length,\n        sampleRecord: trainingList[0]\n    });\n    return trainingList.map((training)=>{\n        var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n        // Enhanced vessel data transformation with position information\n        let completeVesselData = training.vessel || {\n            id: 0,\n            title: \"Unknown\"\n        };\n        if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n            try {\n                // Get complete vessel data including position, icon, and other metadata\n                completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                // Ensure we preserve original vessel data if transformation fails\n                if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                    completeVesselData = training.vessel;\n                }\n                // Add position information if available\n                if (training.vessel.position && !completeVesselData.position) {\n                    completeVesselData.position = training.vessel.position;\n                }\n                // Add location type if available\n                if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                    completeVesselData.trainingLocationType = training.trainingLocationType;\n                }\n            } catch (error) {\n                console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                completeVesselData = training.vessel;\n            }\n        }\n        // Enhanced member deduplication and normalization\n        const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n        const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n            // Check if member already exists in the accumulator\n            const existingMember = acc.find((m)=>m.id === member.id);\n            if (existingMember) {\n                // Update existing member with more complete data\n                existingMember.firstName = member.firstName || existingMember.firstName;\n                existingMember.surname = member.surname || existingMember.surname;\n                existingMember.email = member.email || existingMember.email;\n            } else {\n                // Add new member with normalized data\n                acc.push({\n                    id: member.id,\n                    firstName: member.firstName || \"\",\n                    surname: member.surname || \"\",\n                    email: member.email || \"\",\n                    ...member // Preserve any additional member data\n                });\n            }\n            return acc;\n        }, []);\n        return {\n            id: training.id,\n            dueDate: training.date,\n            vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n            vessel: completeVesselData,\n            trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n            trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: deduplicatedMembers,\n            status: {\n                label: \"Completed\",\n                isOverdue: false,\n                class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                dueWithinSevenDays: false\n            },\n            category: \"completed\",\n            originalData: training\n        };\n    });\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Raw training session dues input:\", {\n        totalRecords: trainingSessionDues.length,\n        sampleRecord: trainingSessionDues[0]\n    });\n    // Apply the same filtering logic as CrewTrainingList\n    // Filter out crew members who are no longer assigned to the vessel\n    const filteredData = trainingSessionDues.filter((item)=>{\n        var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n        return (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n            return m.id === item.memberID;\n        });\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n        originalCount: trainingSessionDues.length,\n        filteredCount: filteredData.length,\n        removedCount: trainingSessionDues.length - filteredData.length\n    });\n    // Add status to each record\n    const dueWithStatus = filteredData.map((due)=>{\n        const status = (0,_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n        return {\n            ...due,\n            status\n        };\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n        totalRecords: dueWithStatus.length,\n        statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n            const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n            acc[key] = (acc[key] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    // Apply the same grouping logic as CrewTrainingList\n    // Group by vessel-trainingType-dueDate\n    const groupedDues = dueWithStatus.reduce((acc, due)=>{\n        const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n        if (!acc[key]) {\n            var _due_trainingSession;\n            acc[key] = {\n                id: due.id,\n                vesselID: due.vesselID,\n                vessel: due.vessel,\n                trainingTypeID: due.trainingTypeID,\n                trainingType: due.trainingType,\n                dueDate: due.dueDate,\n                status: due.status,\n                trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                members: []\n            };\n        }\n        acc[key].members.push(due.member);\n        return acc;\n    }, {});\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n        groupCount: Object.keys(groupedDues).length,\n        groupKeys: Object.keys(groupedDues)\n    });\n    // Merge members within each group (same as CrewTrainingList)\n    const mergedDues = Object.values(groupedDues).map((group)=>{\n        const mergedMembers = group.members.reduce((acc, member)=>{\n            const existingMember = acc.find((m)=>m.id === member.id);\n            if (existingMember) {\n                existingMember.firstName = member.firstName;\n                existingMember.surname = member.surname;\n            } else {\n                acc.push(member);\n            }\n            return acc;\n        }, []);\n        // Determine category based on status\n        let category;\n        if (group.status.isOverdue) {\n            category = \"overdue\";\n        } else if (group.status.dueWithinSevenDays) {\n            category = \"upcoming\";\n        } else {\n            category = \"upcoming\" // Default for future due dates\n            ;\n        }\n        return {\n            id: group.id,\n            dueDate: group.dueDate,\n            vesselID: group.vesselID,\n            vessel: group.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            trainingTypeID: group.trainingTypeID,\n            trainingType: group.trainingType || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: mergedMembers,\n            status: group.status,\n            category,\n            originalData: group\n        };\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n        totalRecords: mergedDues.length,\n        categoryBreakdown: mergedDues.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {}),\n        sampleRecord: mergedDues[0]\n    });\n    return mergedDues;\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID\n * @param data - Array of unified training data\n * @returns Deduplicated array\n */ const deduplicateTrainingData = (data)=>{\n    const seen = new Set();\n    return data.filter((item)=>{\n        if (seen.has(item.id)) {\n            return false;\n        }\n        seen.add(item.id);\n        return true;\n    });\n};\n/**\n * Sort unified training data with priority-based ordering\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    return data.sort((a, b)=>{\n        // First sort by priority (overdue > upcoming > completed)\n        const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n        if (priorityDiff !== 0) {\n            return priorityDiff;\n        }\n        // Within same priority, sort by date\n        const dateA = new Date(a.dueDate).getTime();\n        const dateB = new Date(b.dueDate).getTime();\n        if (a.category === \"overdue\") {\n            // For overdue: most overdue first (earliest due date first)\n            return dateA - dateB;\n        } else if (a.category === \"upcoming\") {\n            // For upcoming: soonest due date first\n            return dateA - dateB;\n        } else {\n            // For completed: most recent completion first (latest date first)\n            return dateB - dateA;\n        }\n    });\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    try {\n        // Debug logging removed for performance\n        // Transform overdue/upcoming training data\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        // Transform completed training data if requested\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        // Debug logging removed for performance\n        // Remove duplicates and sort with priority-based ordering\n        const deduplicatedData = deduplicateTrainingData(combinedData);\n        const sortedData = sortUnifiedTrainingData(deduplicatedData);\n        // Debug logging removed for performance\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/crew-training-utils.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/lib/crew-training-utils.ts":
/*!********************************************!*\
  !*** ./src/app/lib/crew-training-utils.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * Optimized version with better error handling and performance\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No completed training data provided\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Transforming completed training data:\", {\n        totalRecords: trainingList.length,\n        sampleRecord: trainingList[0]\n    });\n    try {\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n            // Enhanced vessel data transformation with position information\n            let completeVesselData = training.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n                try {\n                    // Get complete vessel data including position, icon, and other metadata\n                    completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                    // Ensure we preserve original vessel data if transformation fails\n                    if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                        completeVesselData = training.vessel;\n                    }\n                    // Add position information if available\n                    if (training.vessel.position && !completeVesselData.position) {\n                        completeVesselData.position = training.vessel.position;\n                    }\n                    // Add location type if available\n                    if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                        completeVesselData.trainingLocationType = training.trainingLocationType;\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                    completeVesselData = training.vessel;\n                }\n            }\n            // Enhanced member deduplication and normalization\n            const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n            const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n                // Check if member already exists in the accumulator\n                const existingMember = acc.find((m)=>m.id === member.id);\n                if (existingMember) {\n                    // Update existing member with more complete data\n                    existingMember.firstName = member.firstName || existingMember.firstName;\n                    existingMember.surname = member.surname || existingMember.surname;\n                    existingMember.email = member.email || existingMember.email;\n                } else {\n                    // Add new member with normalized data\n                    acc.push({\n                        id: member.id,\n                        firstName: member.firstName || \"\",\n                        surname: member.surname || \"\",\n                        email: member.email || \"\",\n                        ...member // Preserve any additional member data\n                    });\n                }\n                return acc;\n            }, []);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n                vessel: completeVesselData,\n                trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: deduplicatedMembers,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                category: \"completed\",\n                originalData: training\n            };\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error transforming completed training data:\", error);\n        return [];\n    }\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No training session dues provided\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Raw training session dues input:\", {\n        totalRecords: trainingSessionDues.length,\n        sampleRecord: trainingSessionDues[0]\n    });\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item but log it\n            if (!hasValidVesselMembers) {\n                var _item_vessel_seaLogsMembers_nodes1, _item_vessel_seaLogsMembers1, _item_vessel1;\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Item without vessel member match:\", {\n                    memberID: item.memberID,\n                    vesselID: item.vesselID,\n                    vesselMembers: (_item_vessel1 = item.vessel) === null || _item_vessel1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers1 = _item_vessel1.seaLogsMembers) === null || _item_vessel_seaLogsMembers1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes1 = _item_vessel_seaLogsMembers1.nodes) === null || _item_vessel_seaLogsMembers_nodes1 === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes1.map((m)=>m === null || m === void 0 ? void 0 : m.id)\n                });\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n            originalCount: trainingSessionDues.length,\n            filteredCount: filteredData.length,\n            removedCount: trainingSessionDues.length - filteredData.length\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n            totalRecords: dueWithStatus.length,\n            statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n                const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n                acc[key] = (acc[key] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n            groupCount: Object.keys(groupedDues).length,\n            groupKeys: Object.keys(groupedDues)\n        });\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            var _group_status, _group_status1;\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            group.members.forEach((member)=>{\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            // Determine category based on status\n            let category;\n            if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                category = \"overdue\";\n            } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                category = \"upcoming\";\n            } else {\n                category = \"upcoming\" // Default for future due dates\n                ;\n            }\n            // Enhanced vessel data with position information\n            const enhancedVessel = group.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            // Add training location type if available\n            if (group.trainingLocationType && !enhancedVessel.trainingLocationType) {\n                enhancedVessel.trainingLocationType = group.trainingLocationType;\n            }\n            return {\n                id: group.id,\n                dueDate: group.dueDate,\n                vesselID: group.vesselID,\n                vessel: enhancedVessel,\n                trainingTypeID: group.trainingTypeID,\n                trainingType: group.trainingType || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: mergedMembers,\n                status: group.status,\n                category,\n                originalData: group\n            };\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n            totalRecords: mergedDues.length,\n            categoryBreakdown: mergedDues.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: mergedDues[0]\n        });\n        return mergedDues;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * Optimized version using Map for better performance\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for deduplication\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting deduplication:\", {\n        totalRecords: data.length,\n        sampleRecord: data[0]\n    });\n    try {\n        // Use Map for O(1) lookups instead of O(n) findIndex\n        const recordMap = new Map();\n        for (const item of data){\n            // Handle both string and number IDs\n            const itemId = typeof item.id === \"string\" ? parseInt(item.id, 10) : item.id;\n            if (!item || !itemId && itemId !== 0 || isNaN(itemId)) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item in deduplication:\", item);\n                continue;\n            }\n            if (recordMap.has(itemId)) {\n                var // Add existing members\n                _existing_members, // Add/merge new members\n                _item_members, _item_vessel, _item_trainingType, _item_status;\n                // Merge with existing record\n                const existing = recordMap.get(itemId);\n                // Use Map for member deduplication for better performance\n                const memberMap = new Map();\n                (_existing_members = existing.members) === null || _existing_members === void 0 ? void 0 : _existing_members.forEach((member)=>{\n                    if (member && member.id) {\n                        memberMap.set(member.id, member);\n                    }\n                });\n                (_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.forEach((member)=>{\n                    if (member && member.id) {\n                        const existingMember = memberMap.get(member.id);\n                        if (existingMember) {\n                            // Merge member data, preferring non-empty values\n                            memberMap.set(member.id, {\n                                ...existingMember,\n                                firstName: member.firstName || existingMember.firstName,\n                                surname: member.surname || existingMember.surname,\n                                email: member.email || existingMember.email,\n                                ...member // Preserve any additional member data\n                            });\n                        } else {\n                            memberMap.set(member.id, member);\n                        }\n                    }\n                });\n                // Update the existing record with merged data\n                recordMap.set(item.id, {\n                    ...existing,\n                    members: Array.from(memberMap.values()),\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) && item.vessel.title !== \"Unknown\" ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) && item.trainingType.title !== \"Unknown\" ? item.trainingType : existing.trainingType,\n                    // Prefer overdue status over upcoming/completed\n                    status: ((_item_status = item.status) === null || _item_status === void 0 ? void 0 : _item_status.isOverdue) ? item.status : existing.status\n                });\n            } else {\n                // Add new record\n                recordMap.set(item.id, item);\n            }\n        }\n        const deduplicatedData = Array.from(recordMap.values());\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Deduplication complete:\", {\n            originalCount: data.length,\n            deduplicatedCount: deduplicatedData.length,\n            removedDuplicates: data.length - deduplicatedData.length,\n            categoryBreakdown: deduplicatedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        return deduplicatedData;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in deduplication:\", error);\n        return data // Return original data if deduplication fails\n        ;\n    }\n};\n/**\n * Sort unified training data with priority-based ordering\n * Optimized version with better performance and error handling\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for sorting\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting sort:\", {\n        totalRecords: data.length,\n        categories: data.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    try {\n        return data.sort((a, b)=>{\n            try {\n                // First sort by priority (overdue > upcoming > completed)\n                const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n                if (priorityDiff !== 0) {\n                    return priorityDiff;\n                }\n                // Within same priority, sort by date with error handling\n                const dateA = a.dueDate ? new Date(a.dueDate).getTime() : 0;\n                const dateB = b.dueDate ? new Date(b.dueDate).getTime() : 0;\n                // Handle invalid dates\n                if (isNaN(dateA) && isNaN(dateB)) return 0;\n                if (isNaN(dateA)) return 1;\n                if (isNaN(dateB)) return -1;\n                if (a.category === \"overdue\") {\n                    // For overdue: most overdue first (earliest due date first)\n                    return dateA - dateB;\n                } else if (a.category === \"upcoming\") {\n                    // For upcoming: soonest due date first\n                    return dateA - dateB;\n                } else {\n                    // For completed: most recent completion first (latest date first)\n                    return dateB - dateA;\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error comparing items in sort:\", error, {\n                    a,\n                    b\n                });\n                return 0 // Keep original order if comparison fails\n                ;\n            }\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in sorting:\", error);\n        return data // Return original data if sorting fails\n        ;\n    }\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * Optimized version with comprehensive debugging and error handling\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting merge process:\", {\n        trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n        completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n        includeCompleted,\n        debug\n    });\n    try {\n        // Transform overdue/upcoming training data\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming training session dues...\");\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed dues result:\", {\n            count: transformedDues.length,\n            sample: transformedDues[0]\n        });\n        // Transform completed training data if requested\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming completed training...\");\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed completed result:\", {\n            count: transformedCompleted.length,\n            sample: transformedCompleted[0]\n        });\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Combined data:\", {\n            totalCount: combinedData.length,\n            duesCount: transformedDues.length,\n            completedCount: transformedCompleted.length,\n            sample: combinedData[0]\n        });\n        // Remove duplicates and sort with priority-based ordering\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting deduplication...\");\n        const deduplicatedData = deduplicateTrainingData(combinedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Deduplication complete:\", {\n            beforeCount: combinedData.length,\n            afterCount: deduplicatedData.length,\n            sample: deduplicatedData[0]\n        });\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting sorting...\");\n        const sortedData = sortUnifiedTrainingData(deduplicatedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Final result:\", {\n            totalCount: sortedData.length,\n            categoryBreakdown: sortedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sample: sortedData[0]\n        });\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        console.error(\"❌ Error stack:\", error instanceof Error ? error.stack : \"No stack trace\");\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/crew-training-utils.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/crew-training-utils */ \"(app-pages-browser)/./src/app/lib/crew-training-utils.ts\");\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId, memberId, isVesselView = false, excludeFilters = [] } = param;\n    _s();\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter options state\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            // Extract filter options from completed training data\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            setCompletedTrainingList(data);\n            setVesselIdOptions(vesselIDs);\n            setTrainingTypeIdOptions(trainingTypeIDs);\n            setTrainerIdOptions(trainerIDs);\n            setCrewIdOptions(memberIDs);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 100,\n                limit: 100\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Use training filters hook\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_11__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: ()=>{}\n    });\n    // Memoize the load data function to prevent unnecessary re-renders\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setLoading(true);\n        await loadTrainingSessionDues(filter);\n        await loadTrainingList(page, filter);\n        setLoading(false);\n    }, [\n        filter,\n        page,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, [\n        loadData\n    ]);\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.getTrainingDataStats)(unifiedData);\n    }, [\n        unifiedData\n    ]);\n    const isLoading = loading || duesLoading || completedLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_10__.TrainingListFilter, {\n                    memberId: memberId,\n                    onChange: handleFilterChange,\n                    vesselIdOptions: vesselIdOptions,\n                    trainingTypeIdOptions: trainingTypeIdOptions,\n                    trainerIdOptions: trainerIdOptions,\n                    memberIdOptions: crewIdOptions,\n                    excludeFilters: excludeFilters\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 254,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                            children: \"Crew Training Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mt-1\",\n                                            children: \"Unified view of all training activities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"destructive\",\n                                            type: \"normal\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-current rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"Overdue: \",\n                                                stats.overdue\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"secondary\",\n                                            type: \"normal\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"Upcoming: \",\n                                                stats.upcoming\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"outline\",\n                                            type: \"normal\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"Completed: \",\n                                                stats.completed\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"outline\",\n                                            type: \"normal\",\n                                            children: [\n                                                \"Total: \",\n                                                stats.total\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    size: \"sm\",\n                                    onClick: ()=>setIncludeCompleted(!includeCompleted),\n                                    children: [\n                                        includeCompleted ? \"Hide\" : \"Show\",\n                                        \" Completed\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: loadData,\n                                    disabled: isLoading,\n                                    children: isLoading ? \"Loading...\" : \"Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Loading training data...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 33\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 29\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                trainingSessionDues: trainingSessionDues,\n                                completedTrainingList: completedTrainingList,\n                                getVesselWithIcon: getVesselWithIcon,\n                                includeCompleted: includeCompleted,\n                                memberId: memberId,\n                                isVesselView: isVesselView,\n                                showToolbar: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 21\n                        }, undefined),\n                        !isLoading && unifiedData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Showing \",\n                                    unifiedData.length,\n                                    \" training record\",\n                                    unifiedData.length !== 1 ? \"s\" : \"\",\n                                    \". Data is sorted with overdue trainings first, then upcoming, then completed.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 266,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 252,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"IZHHBFmTHfJAb1oRqWDAviFOIEc=\", false, function() {\n    return [\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_11__.useTrainingFilters\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/lib/crew-training-utils.ts":
/*!********************************************!*\
  !*** ./src/app/lib/crew-training-utils.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * Optimized version with better error handling and performance\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No completed training data provided\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Transforming completed training data:\", {\n        totalRecords: trainingList.length,\n        sampleRecord: trainingList[0]\n    });\n    try {\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n            // Enhanced vessel data transformation with position information\n            let completeVesselData = training.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n                try {\n                    // Get complete vessel data including position, icon, and other metadata\n                    completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                    // Ensure we preserve original vessel data if transformation fails\n                    if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                        completeVesselData = training.vessel;\n                    }\n                    // Add position information if available\n                    if (training.vessel.position && !completeVesselData.position) {\n                        completeVesselData.position = training.vessel.position;\n                    }\n                    // Add location type if available\n                    if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                        completeVesselData.trainingLocationType = training.trainingLocationType;\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                    completeVesselData = training.vessel;\n                }\n            }\n            // Enhanced member deduplication and normalization\n            const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n            const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n                // Check if member already exists in the accumulator\n                const existingMember = acc.find((m)=>m.id === member.id);\n                if (existingMember) {\n                    // Update existing member with more complete data\n                    existingMember.firstName = member.firstName || existingMember.firstName;\n                    existingMember.surname = member.surname || existingMember.surname;\n                    existingMember.email = member.email || existingMember.email;\n                } else {\n                    // Add new member with normalized data\n                    acc.push({\n                        id: member.id,\n                        firstName: member.firstName || \"\",\n                        surname: member.surname || \"\",\n                        email: member.email || \"\",\n                        ...member // Preserve any additional member data\n                    });\n                }\n                return acc;\n            }, []);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n                vessel: completeVesselData,\n                trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: deduplicatedMembers,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                category: \"completed\",\n                originalData: training\n            };\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error transforming completed training data:\", error);\n        return [];\n    }\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No training session dues provided\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Raw training session dues input:\", {\n        totalRecords: trainingSessionDues.length,\n        sampleRecord: trainingSessionDues[0]\n    });\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item but log it\n            if (!hasValidVesselMembers) {\n                var _item_vessel_seaLogsMembers_nodes1, _item_vessel_seaLogsMembers1, _item_vessel1;\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Item without vessel member match:\", {\n                    memberID: item.memberID,\n                    vesselID: item.vesselID,\n                    vesselMembers: (_item_vessel1 = item.vessel) === null || _item_vessel1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers1 = _item_vessel1.seaLogsMembers) === null || _item_vessel_seaLogsMembers1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes1 = _item_vessel_seaLogsMembers1.nodes) === null || _item_vessel_seaLogsMembers_nodes1 === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes1.map((m)=>m === null || m === void 0 ? void 0 : m.id)\n                });\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n            originalCount: trainingSessionDues.length,\n            filteredCount: filteredData.length,\n            removedCount: trainingSessionDues.length - filteredData.length\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n            totalRecords: dueWithStatus.length,\n            statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n                const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n                acc[key] = (acc[key] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n            groupCount: Object.keys(groupedDues).length,\n            groupKeys: Object.keys(groupedDues)\n        });\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            var _group_status, _group_status1;\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            group.members.forEach((member)=>{\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            // Determine category based on status\n            let category;\n            if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                category = \"overdue\";\n            } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                category = \"upcoming\";\n            } else {\n                category = \"upcoming\" // Default for future due dates\n                ;\n            }\n            // Enhanced vessel data with position information\n            const enhancedVessel = group.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            // Add training location type if available\n            if (group.trainingLocationType && !enhancedVessel.trainingLocationType) {\n                enhancedVessel.trainingLocationType = group.trainingLocationType;\n            }\n            return {\n                id: group.id,\n                dueDate: group.dueDate,\n                vesselID: group.vesselID,\n                vessel: enhancedVessel,\n                trainingTypeID: group.trainingTypeID,\n                trainingType: group.trainingType || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: mergedMembers,\n                status: group.status,\n                category,\n                originalData: group\n            };\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n            totalRecords: mergedDues.length,\n            categoryBreakdown: mergedDues.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: mergedDues[0]\n        });\n        return mergedDues;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * Optimized version using Map for better performance\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for deduplication\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting deduplication:\", {\n        totalRecords: data.length,\n        sampleRecord: data[0]\n    });\n    try {\n        // Use Map for O(1) lookups instead of O(n) findIndex\n        const recordMap = new Map();\n        for (const item of data){\n            // Handle both string and number IDs\n            const itemId = typeof item.id === \"string\" ? parseInt(item.id, 10) : item.id;\n            if (!item || !itemId && itemId !== 0 || isNaN(itemId)) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item in deduplication:\", item);\n                continue;\n            }\n            if (recordMap.has(itemId)) {\n                var // Add existing members\n                _existing_members, // Add/merge new members\n                _item_members, _item_vessel, _item_trainingType, _item_status;\n                // Merge with existing record\n                const existing = recordMap.get(itemId);\n                // Use Map for member deduplication for better performance\n                const memberMap = new Map();\n                (_existing_members = existing.members) === null || _existing_members === void 0 ? void 0 : _existing_members.forEach((member)=>{\n                    if (member && member.id) {\n                        memberMap.set(member.id, member);\n                    }\n                });\n                (_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.forEach((member)=>{\n                    if (member && member.id) {\n                        const existingMember = memberMap.get(member.id);\n                        if (existingMember) {\n                            // Merge member data, preferring non-empty values\n                            memberMap.set(member.id, {\n                                ...existingMember,\n                                firstName: member.firstName || existingMember.firstName,\n                                surname: member.surname || existingMember.surname,\n                                email: member.email || existingMember.email,\n                                ...member // Preserve any additional member data\n                            });\n                        } else {\n                            memberMap.set(member.id, member);\n                        }\n                    }\n                });\n                // Update the existing record with merged data\n                recordMap.set(itemId, {\n                    ...existing,\n                    members: Array.from(memberMap.values()),\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) && item.vessel.title !== \"Unknown\" ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) && item.trainingType.title !== \"Unknown\" ? item.trainingType : existing.trainingType,\n                    // Prefer overdue status over upcoming/completed\n                    status: ((_item_status = item.status) === null || _item_status === void 0 ? void 0 : _item_status.isOverdue) ? item.status : existing.status\n                });\n            } else {\n                // Add new record\n                recordMap.set(item.id, item);\n            }\n        }\n        const deduplicatedData = Array.from(recordMap.values());\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Deduplication complete:\", {\n            originalCount: data.length,\n            deduplicatedCount: deduplicatedData.length,\n            removedDuplicates: data.length - deduplicatedData.length,\n            categoryBreakdown: deduplicatedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        return deduplicatedData;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in deduplication:\", error);\n        return data // Return original data if deduplication fails\n        ;\n    }\n};\n/**\n * Sort unified training data with priority-based ordering\n * Optimized version with better performance and error handling\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for sorting\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting sort:\", {\n        totalRecords: data.length,\n        categories: data.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    try {\n        return data.sort((a, b)=>{\n            try {\n                // First sort by priority (overdue > upcoming > completed)\n                const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n                if (priorityDiff !== 0) {\n                    return priorityDiff;\n                }\n                // Within same priority, sort by date with error handling\n                const dateA = a.dueDate ? new Date(a.dueDate).getTime() : 0;\n                const dateB = b.dueDate ? new Date(b.dueDate).getTime() : 0;\n                // Handle invalid dates\n                if (isNaN(dateA) && isNaN(dateB)) return 0;\n                if (isNaN(dateA)) return 1;\n                if (isNaN(dateB)) return -1;\n                if (a.category === \"overdue\") {\n                    // For overdue: most overdue first (earliest due date first)\n                    return dateA - dateB;\n                } else if (a.category === \"upcoming\") {\n                    // For upcoming: soonest due date first\n                    return dateA - dateB;\n                } else {\n                    // For completed: most recent completion first (latest date first)\n                    return dateB - dateA;\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error comparing items in sort:\", error, {\n                    a,\n                    b\n                });\n                return 0 // Keep original order if comparison fails\n                ;\n            }\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in sorting:\", error);\n        return data // Return original data if sorting fails\n        ;\n    }\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * Optimized version with comprehensive debugging and error handling\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting merge process:\", {\n        trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n        completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n        includeCompleted,\n        debug\n    });\n    try {\n        // Transform overdue/upcoming training data\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming training session dues...\");\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed dues result:\", {\n            count: transformedDues.length,\n            sample: transformedDues[0]\n        });\n        // Transform completed training data if requested\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming completed training...\");\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed completed result:\", {\n            count: transformedCompleted.length,\n            sample: transformedCompleted[0]\n        });\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Combined data:\", {\n            totalCount: combinedData.length,\n            duesCount: transformedDues.length,\n            completedCount: transformedCompleted.length,\n            sample: combinedData[0]\n        });\n        // Remove duplicates and sort with priority-based ordering\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting deduplication...\");\n        const deduplicatedData = deduplicateTrainingData(combinedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Deduplication complete:\", {\n            beforeCount: combinedData.length,\n            afterCount: deduplicatedData.length,\n            sample: deduplicatedData[0]\n        });\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting sorting...\");\n        const sortedData = sortUnifiedTrainingData(deduplicatedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Final result:\", {\n            totalCount: sortedData.length,\n            categoryBreakdown: sortedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sample: sortedData[0]\n        });\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        console.error(\"❌ Error stack:\", error instanceof Error ? error.stack : \"No stack trace\");\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/crew-training-utils.ts\n"));

/***/ })

});
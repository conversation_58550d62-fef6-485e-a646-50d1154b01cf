"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/lib/crew-training-utils.ts":
/*!********************************************!*\
  !*** ./src/app/lib/crew-training-utils.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    // Debug logging removed for performance\n    return trainingList.map((training)=>{\n        var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n        // Ensure vessel has complete data including position if function is provided\n        const completeVesselData = getVesselWithIcon ? getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel) : training.vessel || {\n            id: 0,\n            title: \"Unknown\"\n        };\n        return {\n            id: training.id,\n            dueDate: training.date,\n            vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n            vessel: completeVesselData,\n            trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n            trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n            status: {\n                label: \"Completed\",\n                isOverdue: false,\n                class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                dueWithinSevenDays: false\n            },\n            category: \"completed\",\n            originalData: training\n        };\n    });\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Raw training session dues input:\", {\n        totalRecords: trainingSessionDues.length,\n        sampleRecord: trainingSessionDues[0]\n    });\n    // Apply the same filtering logic as CrewTrainingList\n    // Filter out crew members who are no longer assigned to the vessel\n    const filteredData = trainingSessionDues.filter((item)=>{\n        var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n        return (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n            return m.id === item.memberID;\n        });\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n        originalCount: trainingSessionDues.length,\n        filteredCount: filteredData.length,\n        removedCount: trainingSessionDues.length - filteredData.length\n    });\n    // Add status to each record\n    const dueWithStatus = filteredData.map((due)=>{\n        const status = (0,_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n        return {\n            ...due,\n            status\n        };\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n        totalRecords: dueWithStatus.length,\n        statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n            const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n            acc[key] = (acc[key] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    // Apply the same grouping logic as CrewTrainingList\n    // Group by vessel-trainingType-dueDate\n    const groupedDues = dueWithStatus.reduce((acc, due)=>{\n        const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n        if (!acc[key]) {\n            var _due_trainingSession;\n            acc[key] = {\n                id: due.id,\n                vesselID: due.vesselID,\n                vessel: due.vessel,\n                trainingTypeID: due.trainingTypeID,\n                trainingType: due.trainingType,\n                dueDate: due.dueDate,\n                status: due.status,\n                trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                members: []\n            };\n        }\n        acc[key].members.push(due.member);\n        return acc;\n    }, {});\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n        groupCount: Object.keys(groupedDues).length,\n        groupKeys: Object.keys(groupedDues)\n    });\n    // Merge members within each group (same as CrewTrainingList)\n    const mergedDues = Object.values(groupedDues).map((group)=>{\n        const mergedMembers = group.members.reduce((acc, member)=>{\n            const existingMember = acc.find((m)=>m.id === member.id);\n            if (existingMember) {\n                existingMember.firstName = member.firstName;\n                existingMember.surname = member.surname;\n            } else {\n                acc.push(member);\n            }\n            return acc;\n        }, []);\n        // Determine category based on status\n        let category;\n        if (group.status.isOverdue) {\n            category = \"overdue\";\n        } else if (group.status.dueWithinSevenDays) {\n            category = \"upcoming\";\n        } else {\n            category = \"upcoming\" // Default for future due dates\n            ;\n        }\n        return {\n            id: group.id,\n            dueDate: group.dueDate,\n            vesselID: group.vesselID,\n            vessel: group.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            trainingTypeID: group.trainingTypeID,\n            trainingType: group.trainingType || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: mergedMembers,\n            status: group.status,\n            category,\n            originalData: group\n        };\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n        totalRecords: mergedDues.length,\n        categoryBreakdown: mergedDues.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {}),\n        sampleRecord: mergedDues[0]\n    });\n    return mergedDues;\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID\n * @param data - Array of unified training data\n * @returns Deduplicated array\n */ const deduplicateTrainingData = (data)=>{\n    const seen = new Set();\n    return data.filter((item)=>{\n        if (seen.has(item.id)) {\n            return false;\n        }\n        seen.add(item.id);\n        return true;\n    });\n};\n/**\n * Sort unified training data with priority-based ordering\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    return data.sort((a, b)=>{\n        // First sort by priority (overdue > upcoming > completed)\n        const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n        if (priorityDiff !== 0) {\n            return priorityDiff;\n        }\n        // Within same priority, sort by date\n        const dateA = new Date(a.dueDate).getTime();\n        const dateB = new Date(b.dueDate).getTime();\n        if (a.category === \"overdue\") {\n            // For overdue: most overdue first (earliest due date first)\n            return dateA - dateB;\n        } else if (a.category === \"upcoming\") {\n            // For upcoming: soonest due date first\n            return dateA - dateB;\n        } else {\n            // For completed: most recent completion first (latest date first)\n            return dateB - dateA;\n        }\n    });\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    try {\n        // Debug logging removed for performance\n        // Transform overdue/upcoming training data\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        // Transform completed training data if requested\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        // Debug logging removed for performance\n        // Remove duplicates and sort with priority-based ordering\n        const deduplicatedData = deduplicateTrainingData(combinedData);\n        const sortedData = sortUnifiedTrainingData(deduplicatedData);\n        // Debug logging removed for performance\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/crew-training-utils.ts\n"));

/***/ })

});
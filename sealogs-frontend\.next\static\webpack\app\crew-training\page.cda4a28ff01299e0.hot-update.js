"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/lib/crew-training-utils.ts":
/*!********************************************!*\
  !*** ./src/app/lib/crew-training-utils.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Transforming completed training data:\", {\n        totalRecords: trainingList.length,\n        sampleRecord: trainingList[0]\n    });\n    return trainingList.map((training)=>{\n        var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n        // Enhanced vessel data transformation with position information\n        let completeVesselData = training.vessel || {\n            id: 0,\n            title: \"Unknown\"\n        };\n        if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n            try {\n                // Get complete vessel data including position, icon, and other metadata\n                completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                // Ensure we preserve original vessel data if transformation fails\n                if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                    completeVesselData = training.vessel;\n                }\n                // Add position information if available\n                if (training.vessel.position && !completeVesselData.position) {\n                    completeVesselData.position = training.vessel.position;\n                }\n                // Add location type if available\n                if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                    completeVesselData.trainingLocationType = training.trainingLocationType;\n                }\n            } catch (error) {\n                console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                completeVesselData = training.vessel;\n            }\n        }\n        // Enhanced member deduplication and normalization\n        const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n        const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n            // Check if member already exists in the accumulator\n            const existingMember = acc.find((m)=>m.id === member.id);\n            if (existingMember) {\n                // Update existing member with more complete data\n                existingMember.firstName = member.firstName || existingMember.firstName;\n                existingMember.surname = member.surname || existingMember.surname;\n                existingMember.email = member.email || existingMember.email;\n            } else {\n                // Add new member with normalized data\n                acc.push({\n                    id: member.id,\n                    firstName: member.firstName || \"\",\n                    surname: member.surname || \"\",\n                    email: member.email || \"\",\n                    ...member // Preserve any additional member data\n                });\n            }\n            return acc;\n        }, []);\n        return {\n            id: training.id,\n            dueDate: training.date,\n            vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n            vessel: completeVesselData,\n            trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n            trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: deduplicatedMembers,\n            status: {\n                label: \"Completed\",\n                isOverdue: false,\n                class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                dueWithinSevenDays: false\n            },\n            category: \"completed\",\n            originalData: training\n        };\n    });\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No training session dues provided\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Raw training session dues input:\", {\n        totalRecords: trainingSessionDues.length,\n        sampleRecord: trainingSessionDues[0]\n    });\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item but log it\n            if (!hasValidVesselMembers) {\n                var _item_vessel_seaLogsMembers_nodes1, _item_vessel_seaLogsMembers1, _item_vessel1;\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Item without vessel member match:\", {\n                    memberID: item.memberID,\n                    vesselID: item.vesselID,\n                    vesselMembers: (_item_vessel1 = item.vessel) === null || _item_vessel1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers1 = _item_vessel1.seaLogsMembers) === null || _item_vessel_seaLogsMembers1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes1 = _item_vessel_seaLogsMembers1.nodes) === null || _item_vessel_seaLogsMembers_nodes1 === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes1.map((m)=>m === null || m === void 0 ? void 0 : m.id)\n                });\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n            originalCount: trainingSessionDues.length,\n            filteredCount: filteredData.length,\n            removedCount: trainingSessionDues.length - filteredData.length\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n            totalRecords: dueWithStatus.length,\n            statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n                const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n                acc[key] = (acc[key] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n            groupCount: Object.keys(groupedDues).length,\n            groupKeys: Object.keys(groupedDues)\n        });\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            var _group_status, _group_status1;\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            group.members.forEach((member)=>{\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            // Determine category based on status\n            let category;\n            if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                category = \"overdue\";\n            } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                category = \"upcoming\";\n            } else {\n                category = \"upcoming\" // Default for future due dates\n                ;\n            }\n            // Enhanced vessel data with position information\n            const enhancedVessel = group.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            // Add training location type if available\n            if (group.trainingLocationType && !enhancedVessel.trainingLocationType) {\n                enhancedVessel.trainingLocationType = group.trainingLocationType;\n            }\n            return {\n                id: group.id,\n                dueDate: group.dueDate,\n                vesselID: group.vesselID,\n                vessel: enhancedVessel,\n                trainingTypeID: group.trainingTypeID,\n                trainingType: group.trainingType || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: mergedMembers,\n                status: group.status,\n                category,\n                originalData: group\n            };\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n            totalRecords: mergedDues.length,\n            categoryBreakdown: mergedDues.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: mergedDues[0]\n        });\n        return mergedDues;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    const seenIds = new Set();\n    const deduplicatedData = [];\n    for (const item of data){\n        if (seenIds.has(item.id)) {\n            // Find existing item and merge member data\n            const existingIndex = deduplicatedData.findIndex((existing)=>existing.id === item.id);\n            if (existingIndex !== -1) {\n                var _item_vessel, _item_trainingType;\n                const existing = deduplicatedData[existingIndex];\n                // Merge members from both records\n                const combinedMembers = [\n                    ...existing.members || [],\n                    ...item.members || []\n                ];\n                const uniqueMembers = combinedMembers.reduce((acc, member)=>{\n                    const existingMember = acc.find((m)=>m.id === member.id);\n                    if (!existingMember) {\n                        acc.push(member);\n                    }\n                    return acc;\n                }, []);\n                // Update the existing record with merged data\n                deduplicatedData[existingIndex] = {\n                    ...existing,\n                    members: uniqueMembers,\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) ? item.trainingType : existing.trainingType\n                };\n            }\n        } else {\n            seenIds.add(item.id);\n            deduplicatedData.push(item);\n        }\n    }\n    return deduplicatedData;\n};\n/**\n * Sort unified training data with priority-based ordering\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    return data.sort((a, b)=>{\n        // First sort by priority (overdue > upcoming > completed)\n        const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n        if (priorityDiff !== 0) {\n            return priorityDiff;\n        }\n        // Within same priority, sort by date\n        const dateA = new Date(a.dueDate).getTime();\n        const dateB = new Date(b.dueDate).getTime();\n        if (a.category === \"overdue\") {\n            // For overdue: most overdue first (earliest due date first)\n            return dateA - dateB;\n        } else if (a.category === \"upcoming\") {\n            // For upcoming: soonest due date first\n            return dateA - dateB;\n        } else {\n            // For completed: most recent completion first (latest date first)\n            return dateB - dateA;\n        }\n    });\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    try {\n        // Debug logging removed for performance\n        // Transform overdue/upcoming training data\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        // Transform completed training data if requested\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        // Debug logging removed for performance\n        // Remove duplicates and sort with priority-based ordering\n        const deduplicatedData = deduplicateTrainingData(combinedData);\n        const sortedData = sortUnifiedTrainingData(deduplicatedData);\n        // Debug logging removed for performance\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/crew-training-utils.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-table.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingTable: function() { return /* binding */ UnifiedTrainingTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/crew-training-utils */ \"(app-pages-browser)/./src/app/lib/crew-training-utils.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\nconst getRowStatus = (rowData)=>{\n    if (rowData.status.isOverdue) {\n        return \"overdue\";\n    }\n    if (rowData.status.dueWithinSevenDays) {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\nconst UnifiedMobileTrainingCard = (param)=>{\n    let { data, memberId } = param;\n    var _data_trainingType, _data_vessel, _data_status, _data_originalData;\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const isCompleted = data.category === \"completed\";\n    const isOverdue = data.category === \"overdue\";\n    const isUpcoming = data.category === \"upcoming\";\n    const members = data.members || [];\n    const trainingTitle = ((_data_trainingType = data.trainingType) === null || _data_trainingType === void 0 ? void 0 : _data_trainingType.title) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-3 tablet-md:border-none border-b border-border py-3 small:pe-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap justify-between items-center\",\n                children: [\n                    isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/crew-training/info?id=\".concat(data.id),\n                        className: \"font-semibold text-base hover:text-primary\",\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"font-semibold text-base\", isOverdue && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                        children: trainingTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 items-center landscape:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                className: \"text-sm m-0 text-muted-foreground\",\n                                children: \"Status:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-sm font-medium px-2 py-1 rounded-md\", data.status.isOverdue ? \"bg-destructive/10 text-destructive\" : data.status.dueWithinSevenDays ? \"bg-warning/10 text-warning\" : \"bg-muted/50 text-muted-foreground\"),\n                                children: data.status.label || data.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 81,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"tablet-md:hidden space-y-[7px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Training Details:\" : \"Due Date:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-sm\", isOverdue && \"text-cinnabar-500\"),\n                        children: isCompleted ? trainingTitle : data.dueDate ? formatDate(data.dueDate) : \"Not specified\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                position: \"left\",\n                className: \"text-sm laptop:hidden text-muted-foreground\",\n                label: isCompleted ? \"Team:\" : \"Crew Members:\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 flex-wrap\",\n                    children: [\n                        members.slice(0, bp.isTabletMd ? 8 : 6).map((member)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: !isCompleted && isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, member.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 29\n                            }, undefined);\n                        }),\n                        members.length > (bp.isTabletMd ? 8 : 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                                    className: \"w-fit\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-fit h-8\",\n                                        children: [\n                                            \"+\",\n                                            members.length - (bp.isTabletMd ? 8 : 6),\n                                            \" \",\n                                            \"more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                                    className: \"w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 max-h-64 overflow-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: members.slice(bp.isTabletMd ? 8 : 6).map((remainingMember)=>/*#__PURE__*/ {\n                                                var _remainingMember_firstName, _remainingMember_surname;\n                                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                            size: \"xs\",\n                                                            variant: \"secondary\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                className: \"text-xs\",\n                                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(remainingMember.firstName, remainingMember.surname)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        \"\".concat((_remainingMember_firstName = remainingMember.firstName) !== null && _remainingMember_firstName !== void 0 ? _remainingMember_firstName : \"\", \" \").concat((_remainingMember_surname = remainingMember.surname) !== null && _remainingMember_surname !== void 0 ? _remainingMember_surname : \"\")\n                                                    ]\n                                                }, remainingMember.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 49\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between landscape:hidden items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Location:\" : \"Vessel:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_data_vessel = data.vessel) === null || _data_vessel === void 0 ? void 0 : _data_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: data.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 202,\n                columnNumber: 13\n            }, undefined),\n            !bp.isTabletMd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n                        className: \"my-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Category: \",\n                                    data.category\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 25\n                            }, undefined),\n                            ((_data_status = data.status) === null || _data_status === void 0 ? void 0 : _data_status.daysUntilDue) !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: data.status.daysUntilDue > 0 ? \"Due in \".concat(data.status.daysUntilDue, \" days\") : data.status.daysUntilDue === 0 ? \"Due today\" : \"\".concat(Math.abs(data.status.daysUntilDue), \" days overdue\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true),\n            isCompleted && ((_data_originalData = data.originalData) === null || _data_originalData === void 0 ? void 0 : _data_originalData.trainer) && !bp.isLandscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center pt-2 border-t border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Trainer:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                size: \"sm\",\n                                variant: \"secondary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                    className: \"text-sm\",\n                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(data.originalData.trainer.firstName, data.originalData.trainer.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: [\n                                    data.originalData.trainer.firstName,\n                                    \" \",\n                                    data.originalData.trainer.surname\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 238,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 80,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedMobileTrainingCard, \"ZxSHrfPd9jdclp97BkZrjvTJck4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints\n    ];\n});\n_c = UnifiedMobileTrainingCard;\nconst UnifiedTrainingTable = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, memberId, isVesselView = false, showToolbar = false, pageSize } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery)(\"(min-width: 720px)\");\n    // Use the utility function to merge and sort data\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Determine if we have mixed data types or primarily one type\n    const hasOverdueOrUpcoming = unifiedData.some((item)=>item.category === \"overdue\" || item.category === \"upcoming\");\n    const hasCompleted = unifiedData.some((item)=>item.category === \"completed\");\n    const isUnifiedView = hasOverdueOrUpcoming && hasCompleted;\n    // Create different column sets based on data type\n    const getColumnsForDataType = ()=>{\n        // Common mobile training card column\n        const mobileColumn = {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Training\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                    data: training,\n                    memberId: memberId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                // Sort by category priority first, then by date\n                const trainingA = rowA.original;\n                const trainingB = rowB.original;\n                const priorityA = trainingA.category === \"overdue\" ? 1 : trainingA.category === \"upcoming\" ? 2 : 3;\n                const priorityB = trainingB.category === \"overdue\" ? 1 : trainingB.category === \"upcoming\" ? 2 : 3;\n                if (priorityA !== priorityB) {\n                    return priorityA - priorityB;\n                }\n                const dateA = new Date(trainingA.dueDate).getTime();\n                const dateB = new Date(trainingB.dueDate).getTime();\n                return trainingA.category === \"completed\" ? dateB - dateA : dateA - dateB;\n            }\n        };\n        // If we have only completed training, use completed training columns\n        if (hasCompleted && !hasOverdueOrUpcoming) {\n            return [\n                {\n                    accessorKey: \"title\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Completed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    cell: (param)=>{\n                        let { row } = param;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                            data: training,\n                            memberId: memberId\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 29\n                        }, undefined);\n                    },\n                    sortingFn: (rowA, rowB)=>{\n                        var _rowA_original, _rowB_original;\n                        const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || 0).getTime();\n                        const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || 0).getTime();\n                        return dateB - dateA;\n                    }\n                },\n                {\n                    accessorKey: \"trainingDrillsCompleted\",\n                    cellAlignment: \"left\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Training/drills completed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"tablet-md\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_originalData_trainingTypes, _training_originalData, _training_trainingType;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                            children: ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_trainingTypes = _training_originalData.trainingTypes) === null || _training_originalData_trainingTypes === void 0 ? void 0 : _training_originalData_trainingTypes.nodes) ? training.originalData.trainingTypes.nodes.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        item.title,\n                                        \",\\xa0\"\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 47\n                                }, undefined)) : ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 29\n                        }, undefined);\n                    },\n                    sortingFn: (rowA, rowB)=>{\n                        var _rowA_original_originalData_trainingTypes_nodes_, _rowA_original_originalData_trainingTypes_nodes, _rowA_original_originalData_trainingTypes, _rowA_original_originalData, _rowA_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_originalData_trainingTypes_nodes_, _rowB_original_originalData_trainingTypes_nodes, _rowB_original_originalData_trainingTypes, _rowB_original_originalData, _rowB_original, _rowB_original_trainingType, _rowB_original1;\n                        const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes = _rowA_original_originalData.trainingTypes) === null || _rowA_original_originalData_trainingTypes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes = _rowA_original_originalData_trainingTypes.nodes) === null || _rowA_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes_ = _rowA_original_originalData_trainingTypes_nodes[0]) === null || _rowA_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_originalData_trainingTypes_nodes_.title) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                        const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes = _rowB_original_originalData.trainingTypes) === null || _rowB_original_originalData_trainingTypes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes = _rowB_original_originalData_trainingTypes.nodes) === null || _rowB_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes_ = _rowB_original_originalData_trainingTypes_nodes[0]) === null || _rowB_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_originalData_trainingTypes_nodes_.title) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                        return valueA.localeCompare(valueB);\n                    }\n                },\n                {\n                    accessorKey: \"where\",\n                    cellAlignment: \"left\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Where\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_vessel, _training_originalData;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-nowrap\",\n                                    children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainingLocationType) || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                    vessel: training.vessel,\n                                    iconClassName: \"size-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 29\n                        }, undefined);\n                    },\n                    sortingFn: (rowA, rowB)=>{\n                        var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                        const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                        const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                        return valueA.localeCompare(valueB);\n                    }\n                },\n                {\n                    accessorKey: \"trainer\",\n                    cellAlignment: \"center\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Trainer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_originalData;\n                        const training = row.original;\n                        const trainer = (_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainer;\n                        if (!trainer) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 46\n                        }, undefined);\n                        var _trainer_surname, _trainer_surname1;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-nowrap\",\n                            children: !isVesselView ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            trainer.firstName,\n                                            \" \",\n                                            (_trainer_surname = trainer.surname) !== null && _trainer_surname !== void 0 ? _trainer_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 41\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 37\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            trainer.firstName,\n                                            \" \",\n                                            (_trainer_surname1 = trainer.surname) !== null && _trainer_surname1 !== void 0 ? _trainer_surname1 : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 41\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 37\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 29\n                        }, undefined);\n                    },\n                    sortingFn: (rowA, rowB)=>{\n                        var _rowA_original_originalData_trainer, _rowA_original_originalData, _rowA_original, _rowA_original_originalData_trainer1, _rowA_original_originalData1, _rowA_original1, _rowB_original_originalData_trainer, _rowB_original_originalData, _rowB_original, _rowB_original_originalData_trainer1, _rowB_original_originalData1, _rowB_original1;\n                        const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainer = _rowA_original_originalData.trainer) === null || _rowA_original_originalData_trainer === void 0 ? void 0 : _rowA_original_originalData_trainer.firstName) || \"\", \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_originalData1 = _rowA_original1.originalData) === null || _rowA_original_originalData1 === void 0 ? void 0 : (_rowA_original_originalData_trainer1 = _rowA_original_originalData1.trainer) === null || _rowA_original_originalData_trainer1 === void 0 ? void 0 : _rowA_original_originalData_trainer1.surname) || \"\") || \"\";\n                        const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainer = _rowB_original_originalData.trainer) === null || _rowB_original_originalData_trainer === void 0 ? void 0 : _rowB_original_originalData_trainer.firstName) || \"\", \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_originalData1 = _rowB_original1.originalData) === null || _rowB_original_originalData1 === void 0 ? void 0 : (_rowB_original_originalData_trainer1 = _rowB_original_originalData1.trainer) === null || _rowB_original_originalData_trainer1 === void 0 ? void 0 : _rowB_original_originalData_trainer1.surname) || \"\") || \"\";\n                        return valueA.localeCompare(valueB);\n                    }\n                },\n                {\n                    accessorKey: \"who\",\n                    cellAlignment: \"right\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Who\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"laptop\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_originalData_members, _training_originalData;\n                        const training = row.original;\n                        const members = ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_members = _training_originalData.members) === null || _training_originalData_members === void 0 ? void 0 : _training_originalData_members.nodes) || training.members || [];\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex items-end gap-1\",\n                            children: members.map((member, index)=>/*#__PURE__*/ {\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                size: \"sm\",\n                                                variant: \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 37\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 29\n                        }, undefined);\n                    },\n                    sortingFn: (rowA, rowB)=>{\n                        var _rowA_original_originalData_members, _rowA_original_originalData, _rowA_original, _rowA_original1, _rowB_original_originalData_members, _rowB_original_originalData, _rowB_original, _rowB_original1, _membersA_, _membersA_1, _membersB_, _membersB_1;\n                        const membersA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_members = _rowA_original_originalData.members) === null || _rowA_original_originalData_members === void 0 ? void 0 : _rowA_original_originalData_members.nodes) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.members) || [];\n                        const membersB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_members = _rowB_original_originalData.members) === null || _rowB_original_originalData_members === void 0 ? void 0 : _rowB_original_originalData_members.nodes) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.members) || [];\n                        var _membersA__firstName, _membersA__surname;\n                        const valueA = \"\".concat((_membersA__firstName = membersA === null || membersA === void 0 ? void 0 : (_membersA_ = membersA[0]) === null || _membersA_ === void 0 ? void 0 : _membersA_.firstName) !== null && _membersA__firstName !== void 0 ? _membersA__firstName : \"\", \" \").concat((_membersA__surname = membersA === null || membersA === void 0 ? void 0 : (_membersA_1 = membersA[0]) === null || _membersA_1 === void 0 ? void 0 : _membersA_1.surname) !== null && _membersA__surname !== void 0 ? _membersA__surname : \"\") || \"\";\n                        var _membersB__firstName, _membersB__surname;\n                        const valueB = \"\".concat((_membersB__firstName = membersB === null || membersB === void 0 ? void 0 : (_membersB_ = membersB[0]) === null || _membersB_ === void 0 ? void 0 : _membersB_.firstName) !== null && _membersB__firstName !== void 0 ? _membersB__firstName : \"\", \" \").concat((_membersB__surname = membersB === null || membersB === void 0 ? void 0 : (_membersB_1 = membersB[0]) === null || _membersB_1 === void 0 ? void 0 : _membersB_1.surname) !== null && _membersB__surname !== void 0 ? _membersB__surname : \"\") || \"\";\n                        return valueA.localeCompare(valueB);\n                    }\n                }\n            ];\n        }\n        // If we have only overdue/upcoming training, use overdue training columns\n        if (hasOverdueOrUpcoming && !hasCompleted) {\n            return [\n                mobileColumn,\n                {\n                    accessorKey: \"vessel\",\n                    cellAlignment: \"left\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Vessel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_vessel;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: !isVesselView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 37\n                            }, undefined)\n                        }, void 0, false);\n                    }\n                },\n                {\n                    accessorKey: \"crew\",\n                    cellAlignment: \"right\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Crew\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"laptop\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_status;\n                        const training = row.original;\n                        const members = training.members || [];\n                        return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-1\",\n                            children: members.map((member)=>/*#__PURE__*/ {\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                size: \"sm\",\n                                                variant: training.status.isOverdue ? \"destructive\" : \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, member.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 37\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 29\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"!rounded-full size-10\", (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.class),\n                            children: members.length\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                },\n                {\n                    accessorKey: \"status\",\n                    cellAlignment: \"right\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_status, _training_status1, _training_status2;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? (_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                            children: ((_training_status2 = training.status) === null || _training_status2 === void 0 ? void 0 : _training_status2.label) || \"Unknown Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                }\n            ];\n        }\n        // Default unified view with all columns\n        return [\n            mobileColumn,\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 683,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_trainingType;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(training.status.isOverdue && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                        children: ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 25\n                    }, undefined);\n                }\n            },\n            {\n                accessorKey: \"dueDate\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-right\", training.status.isOverdue && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                        children: formatDate(training.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 25\n                    }, undefined);\n                }\n            },\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 727,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 733,\n                        columnNumber: 25\n                    }, undefined);\n                }\n            }\n        ];\n    };\n    // Create table columns\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)(getColumnsForDataType()), [\n        memberId,\n        hasOverdueOrUpcoming,\n        hasCompleted,\n        isVesselView,\n        isWide\n    ]);\n    if (!(unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: \"No training data available\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n            lineNumber: 756,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.FilteredTable, {\n        columns: columns,\n        data: unifiedData,\n        showToolbar: showToolbar,\n        rowStatus: getRowStatus,\n        pageSize: pageSize || 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 763,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(UnifiedTrainingTable, \"x7VOLZGRwr6UzHBlctBEB/GNMsI=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery\n    ];\n});\n_c1 = UnifiedTrainingTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingTable);\nvar _c, _c1;\n$RefreshReg$(_c, \"UnifiedMobileTrainingCard\");\n$RefreshReg$(_c1, \"UnifiedTrainingTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\n"));

/***/ })

});
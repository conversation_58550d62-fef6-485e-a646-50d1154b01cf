"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/lib/crew-training-utils.ts":
/*!********************************************!*\
  !*** ./src/app/lib/crew-training-utils.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Transforming completed training data:\", {\n        totalRecords: trainingList.length,\n        sampleRecord: trainingList[0]\n    });\n    return trainingList.map((training)=>{\n        var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n        // Enhanced vessel data transformation with position information\n        let completeVesselData = training.vessel || {\n            id: 0,\n            title: \"Unknown\"\n        };\n        if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n            try {\n                // Get complete vessel data including position, icon, and other metadata\n                completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                // Ensure we preserve original vessel data if transformation fails\n                if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                    completeVesselData = training.vessel;\n                }\n                // Add position information if available\n                if (training.vessel.position && !completeVesselData.position) {\n                    completeVesselData.position = training.vessel.position;\n                }\n                // Add location type if available\n                if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                    completeVesselData.trainingLocationType = training.trainingLocationType;\n                }\n            } catch (error) {\n                console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                completeVesselData = training.vessel;\n            }\n        }\n        // Enhanced member deduplication and normalization\n        const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n        const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n            // Check if member already exists in the accumulator\n            const existingMember = acc.find((m)=>m.id === member.id);\n            if (existingMember) {\n                // Update existing member with more complete data\n                existingMember.firstName = member.firstName || existingMember.firstName;\n                existingMember.surname = member.surname || existingMember.surname;\n                existingMember.email = member.email || existingMember.email;\n            } else {\n                // Add new member with normalized data\n                acc.push({\n                    id: member.id,\n                    firstName: member.firstName || \"\",\n                    surname: member.surname || \"\",\n                    email: member.email || \"\",\n                    ...member // Preserve any additional member data\n                });\n            }\n            return acc;\n        }, []);\n        return {\n            id: training.id,\n            dueDate: training.date,\n            vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n            vessel: completeVesselData,\n            trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n            trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: deduplicatedMembers,\n            status: {\n                label: \"Completed\",\n                isOverdue: false,\n                class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                dueWithinSevenDays: false\n            },\n            category: \"completed\",\n            originalData: training\n        };\n    });\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Raw training session dues input:\", {\n        totalRecords: trainingSessionDues.length,\n        sampleRecord: trainingSessionDues[0]\n    });\n    // Apply the same filtering logic as CrewTrainingList\n    // Filter out crew members who are no longer assigned to the vessel\n    const filteredData = trainingSessionDues.filter((item)=>{\n        var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n        return (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n            return m.id === item.memberID;\n        });\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n        originalCount: trainingSessionDues.length,\n        filteredCount: filteredData.length,\n        removedCount: trainingSessionDues.length - filteredData.length\n    });\n    // Add status to each record\n    const dueWithStatus = filteredData.map((due)=>{\n        const status = (0,_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n        return {\n            ...due,\n            status\n        };\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n        totalRecords: dueWithStatus.length,\n        statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n            const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n            acc[key] = (acc[key] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    // Apply the same grouping logic as CrewTrainingList\n    // Group by vessel-trainingType-dueDate\n    const groupedDues = dueWithStatus.reduce((acc, due)=>{\n        const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n        if (!acc[key]) {\n            var _due_trainingSession;\n            acc[key] = {\n                id: due.id,\n                vesselID: due.vesselID,\n                vessel: due.vessel,\n                trainingTypeID: due.trainingTypeID,\n                trainingType: due.trainingType,\n                dueDate: due.dueDate,\n                status: due.status,\n                trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                members: []\n            };\n        }\n        acc[key].members.push(due.member);\n        return acc;\n    }, {});\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n        groupCount: Object.keys(groupedDues).length,\n        groupKeys: Object.keys(groupedDues)\n    });\n    // Merge members within each group (same as CrewTrainingList)\n    const mergedDues = Object.values(groupedDues).map((group)=>{\n        const mergedMembers = group.members.reduce((acc, member)=>{\n            const existingMember = acc.find((m)=>m.id === member.id);\n            if (existingMember) {\n                // Update existing member with more complete data\n                existingMember.firstName = member.firstName || existingMember.firstName;\n                existingMember.surname = member.surname || existingMember.surname;\n                existingMember.email = member.email || existingMember.email;\n            } else {\n                // Add new member with normalized data\n                acc.push({\n                    id: member.id,\n                    firstName: member.firstName || \"\",\n                    surname: member.surname || \"\",\n                    email: member.email || \"\",\n                    ...member // Preserve any additional member data\n                });\n            }\n            return acc;\n        }, []);\n        // Determine category based on status\n        let category;\n        if (group.status.isOverdue) {\n            category = \"overdue\";\n        } else if (group.status.dueWithinSevenDays) {\n            category = \"upcoming\";\n        } else {\n            category = \"upcoming\" // Default for future due dates\n            ;\n        }\n        // Enhanced vessel data with position information\n        const enhancedVessel = group.vessel || {\n            id: 0,\n            title: \"Unknown\"\n        };\n        // Add training location type if available\n        if (group.trainingLocationType && !enhancedVessel.trainingLocationType) {\n            enhancedVessel.trainingLocationType = group.trainingLocationType;\n        }\n        return {\n            id: group.id,\n            dueDate: group.dueDate,\n            vesselID: group.vesselID,\n            vessel: enhancedVessel,\n            trainingTypeID: group.trainingTypeID,\n            trainingType: group.trainingType || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: mergedMembers,\n            status: group.status,\n            category,\n            originalData: group\n        };\n    });\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n        totalRecords: mergedDues.length,\n        categoryBreakdown: mergedDues.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {}),\n        sampleRecord: mergedDues[0]\n    });\n    return mergedDues;\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    const seenIds = new Set();\n    const deduplicatedData = [];\n    for (const item of data){\n        if (seenIds.has(item.id)) {\n            // Find existing item and merge member data\n            const existingIndex = deduplicatedData.findIndex((existing)=>existing.id === item.id);\n            if (existingIndex !== -1) {\n                var _item_vessel, _item_trainingType;\n                const existing = deduplicatedData[existingIndex];\n                // Merge members from both records\n                const combinedMembers = [\n                    ...existing.members || [],\n                    ...item.members || []\n                ];\n                const uniqueMembers = combinedMembers.reduce((acc, member)=>{\n                    const existingMember = acc.find((m)=>m.id === member.id);\n                    if (!existingMember) {\n                        acc.push(member);\n                    }\n                    return acc;\n                }, []);\n                // Update the existing record with merged data\n                deduplicatedData[existingIndex] = {\n                    ...existing,\n                    members: uniqueMembers,\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) ? item.trainingType : existing.trainingType\n                };\n            }\n        } else {\n            seenIds.add(item.id);\n            deduplicatedData.push(item);\n        }\n    }\n    return deduplicatedData;\n};\n/**\n * Sort unified training data with priority-based ordering\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    return data.sort((a, b)=>{\n        // First sort by priority (overdue > upcoming > completed)\n        const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n        if (priorityDiff !== 0) {\n            return priorityDiff;\n        }\n        // Within same priority, sort by date\n        const dateA = new Date(a.dueDate).getTime();\n        const dateB = new Date(b.dueDate).getTime();\n        if (a.category === \"overdue\") {\n            // For overdue: most overdue first (earliest due date first)\n            return dateA - dateB;\n        } else if (a.category === \"upcoming\") {\n            // For upcoming: soonest due date first\n            return dateA - dateB;\n        } else {\n            // For completed: most recent completion first (latest date first)\n            return dateB - dateA;\n        }\n    });\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    try {\n        // Debug logging removed for performance\n        // Transform overdue/upcoming training data\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        // Transform completed training data if requested\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        // Debug logging removed for performance\n        // Remove duplicates and sort with priority-based ordering\n        const deduplicatedData = deduplicateTrainingData(combinedData);\n        const sortedData = sortUnifiedTrainingData(deduplicatedData);\n        // Debug logging removed for performance\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/crew-training-utils.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-table.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingTable: function() { return /* binding */ UnifiedTrainingTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/crew-training-utils */ \"(app-pages-browser)/./src/app/lib/crew-training-utils.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\nconst getRowStatus = (rowData)=>{\n    if (rowData.status.isOverdue) {\n        return \"overdue\";\n    }\n    if (rowData.status.dueWithinSevenDays) {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\nconst UnifiedMobileTrainingCard = (param)=>{\n    let { data, memberId } = param;\n    var _data_trainingType, _data_vessel, _data_status, _data_originalData;\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const isCompleted = data.category === \"completed\";\n    const isOverdue = data.category === \"overdue\";\n    const isUpcoming = data.category === \"upcoming\";\n    const members = data.members || [];\n    const trainingTitle = ((_data_trainingType = data.trainingType) === null || _data_trainingType === void 0 ? void 0 : _data_trainingType.title) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-3 tablet-md:border-none border-b border-border py-3 small:pe-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap justify-between items-center\",\n                children: [\n                    isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/crew-training/info?id=\".concat(data.id),\n                        className: \"font-semibold text-base hover:text-primary\",\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"font-semibold text-base\", isOverdue && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                        children: trainingTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 items-center landscape:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                className: \"text-sm m-0 text-muted-foreground\",\n                                children: \"Status:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-sm font-medium px-2 py-1 rounded-md\", data.status.isOverdue ? \"bg-destructive/10 text-destructive\" : data.status.dueWithinSevenDays ? \"bg-warning/10 text-warning\" : \"bg-muted/50 text-muted-foreground\"),\n                                children: data.status.label || data.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 81,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"tablet-md:hidden space-y-[7px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Training Details:\" : \"Due Date:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-sm\", isOverdue && \"text-cinnabar-500\"),\n                        children: isCompleted ? trainingTitle : data.dueDate ? formatDate(data.dueDate) : \"Not specified\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                position: \"left\",\n                className: \"text-sm laptop:hidden text-muted-foreground\",\n                label: isCompleted ? \"Team:\" : \"Crew Members:\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 flex-wrap\",\n                    children: [\n                        members.slice(0, bp.isTabletMd ? 8 : 6).map((member)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: !isCompleted && isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, member.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 29\n                            }, undefined);\n                        }),\n                        members.length > (bp.isTabletMd ? 8 : 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                                    className: \"w-fit\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-fit h-8\",\n                                        children: [\n                                            \"+\",\n                                            members.length - (bp.isTabletMd ? 8 : 6),\n                                            \" \",\n                                            \"more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                                    className: \"w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 max-h-64 overflow-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: members.slice(bp.isTabletMd ? 8 : 6).map((remainingMember)=>/*#__PURE__*/ {\n                                                var _remainingMember_firstName, _remainingMember_surname;\n                                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                            size: \"xs\",\n                                                            variant: \"secondary\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                className: \"text-xs\",\n                                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(remainingMember.firstName, remainingMember.surname)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        \"\".concat((_remainingMember_firstName = remainingMember.firstName) !== null && _remainingMember_firstName !== void 0 ? _remainingMember_firstName : \"\", \" \").concat((_remainingMember_surname = remainingMember.surname) !== null && _remainingMember_surname !== void 0 ? _remainingMember_surname : \"\")\n                                                    ]\n                                                }, remainingMember.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 49\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between landscape:hidden items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Location:\" : \"Vessel:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_data_vessel = data.vessel) === null || _data_vessel === void 0 ? void 0 : _data_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: data.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 202,\n                columnNumber: 13\n            }, undefined),\n            !bp.isTabletMd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n                        className: \"my-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Category: \",\n                                    data.category\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 25\n                            }, undefined),\n                            ((_data_status = data.status) === null || _data_status === void 0 ? void 0 : _data_status.daysUntilDue) !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: data.status.daysUntilDue > 0 ? \"Due in \".concat(data.status.daysUntilDue, \" days\") : data.status.daysUntilDue === 0 ? \"Due today\" : \"\".concat(Math.abs(data.status.daysUntilDue), \" days overdue\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true),\n            isCompleted && ((_data_originalData = data.originalData) === null || _data_originalData === void 0 ? void 0 : _data_originalData.trainer) && !bp.isLandscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center pt-2 border-t border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Trainer:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                size: \"sm\",\n                                variant: \"secondary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                    className: \"text-sm\",\n                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(data.originalData.trainer.firstName, data.originalData.trainer.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: [\n                                    data.originalData.trainer.firstName,\n                                    \" \",\n                                    data.originalData.trainer.surname\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 238,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 80,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedMobileTrainingCard, \"ZxSHrfPd9jdclp97BkZrjvTJck4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints\n    ];\n});\n_c = UnifiedMobileTrainingCard;\nconst UnifiedTrainingTable = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, memberId, isVesselView = false, showToolbar = false, pageSize } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery)(\"(min-width: 720px)\");\n    // Use the utility function to merge and sort data\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Determine if we have mixed data types or primarily one type\n    const hasOverdueOrUpcoming = unifiedData.some((item)=>item.category === \"overdue\" || item.category === \"upcoming\");\n    const hasCompleted = unifiedData.some((item)=>item.category === \"completed\");\n    const isUnifiedView = hasOverdueOrUpcoming && hasCompleted;\n    // Create different column sets based on data type\n    const getColumnsForDataType = ()=>{\n        // Common mobile training card column\n        const mobileColumn = {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Training\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                    data: training,\n                    memberId: memberId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                // Sort by category priority first, then by date\n                const trainingA = rowA.original;\n                const trainingB = rowB.original;\n                const priorityA = trainingA.category === \"overdue\" ? 1 : trainingA.category === \"upcoming\" ? 2 : 3;\n                const priorityB = trainingB.category === \"overdue\" ? 1 : trainingB.category === \"upcoming\" ? 2 : 3;\n                if (priorityA !== priorityB) {\n                    return priorityA - priorityB;\n                }\n                const dateA = new Date(trainingA.dueDate).getTime();\n                const dateB = new Date(trainingB.dueDate).getTime();\n                return trainingA.category === \"completed\" ? dateB - dateA : dateA - dateB;\n            }\n        };\n        // If we have only completed training, use completed training columns\n        if (hasCompleted && !hasOverdueOrUpcoming) {\n            return [\n                {\n                    accessorKey: \"title\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Completed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    cell: (param)=>{\n                        let { row } = param;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                            data: training,\n                            memberId: memberId\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 29\n                        }, undefined);\n                    },\n                    sortingFn: (rowA, rowB)=>{\n                        var _rowA_original, _rowB_original;\n                        const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || 0).getTime();\n                        const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || 0).getTime();\n                        return dateB - dateA;\n                    }\n                },\n                {\n                    accessorKey: \"trainingDrillsCompleted\",\n                    cellAlignment: \"left\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Training/drills completed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"tablet-md\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_originalData_trainingTypes, _training_originalData, _training_trainingType;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                            children: ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_trainingTypes = _training_originalData.trainingTypes) === null || _training_originalData_trainingTypes === void 0 ? void 0 : _training_originalData_trainingTypes.nodes) ? training.originalData.trainingTypes.nodes.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        item.title,\n                                        \",\\xa0\"\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 47\n                                }, undefined)) : ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 29\n                        }, undefined);\n                    },\n                    sortingFn: (rowA, rowB)=>{\n                        var _rowA_original_originalData_trainingTypes_nodes_, _rowA_original_originalData_trainingTypes_nodes, _rowA_original_originalData_trainingTypes, _rowA_original_originalData, _rowA_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_originalData_trainingTypes_nodes_, _rowB_original_originalData_trainingTypes_nodes, _rowB_original_originalData_trainingTypes, _rowB_original_originalData, _rowB_original, _rowB_original_trainingType, _rowB_original1;\n                        const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes = _rowA_original_originalData.trainingTypes) === null || _rowA_original_originalData_trainingTypes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes = _rowA_original_originalData_trainingTypes.nodes) === null || _rowA_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes_ = _rowA_original_originalData_trainingTypes_nodes[0]) === null || _rowA_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_originalData_trainingTypes_nodes_.title) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                        const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes = _rowB_original_originalData.trainingTypes) === null || _rowB_original_originalData_trainingTypes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes = _rowB_original_originalData_trainingTypes.nodes) === null || _rowB_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes_ = _rowB_original_originalData_trainingTypes_nodes[0]) === null || _rowB_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_originalData_trainingTypes_nodes_.title) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                        return valueA.localeCompare(valueB);\n                    }\n                },\n                {\n                    accessorKey: \"where\",\n                    cellAlignment: \"left\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Where\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_vessel, _training_originalData;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-nowrap\",\n                                    children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainingLocationType) || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                    vessel: training.vessel,\n                                    iconClassName: \"size-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 29\n                        }, undefined);\n                    },\n                    sortingFn: (rowA, rowB)=>{\n                        var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                        const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                        const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                        return valueA.localeCompare(valueB);\n                    }\n                },\n                {\n                    accessorKey: \"trainer\",\n                    cellAlignment: \"center\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Trainer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_originalData;\n                        const training = row.original;\n                        const trainer = (_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainer;\n                        if (!trainer) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 46\n                        }, undefined);\n                        var _trainer_surname, _trainer_surname1;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-nowrap\",\n                            children: !isVesselView ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            trainer.firstName,\n                                            \" \",\n                                            (_trainer_surname = trainer.surname) !== null && _trainer_surname !== void 0 ? _trainer_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 41\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 37\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            trainer.firstName,\n                                            \" \",\n                                            (_trainer_surname1 = trainer.surname) !== null && _trainer_surname1 !== void 0 ? _trainer_surname1 : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 41\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 37\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 29\n                        }, undefined);\n                    },\n                    sortingFn: (rowA, rowB)=>{\n                        var _rowA_original_originalData_trainer, _rowA_original_originalData, _rowA_original, _rowA_original_originalData_trainer1, _rowA_original_originalData1, _rowA_original1, _rowB_original_originalData_trainer, _rowB_original_originalData, _rowB_original, _rowB_original_originalData_trainer1, _rowB_original_originalData1, _rowB_original1;\n                        const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainer = _rowA_original_originalData.trainer) === null || _rowA_original_originalData_trainer === void 0 ? void 0 : _rowA_original_originalData_trainer.firstName) || \"\", \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_originalData1 = _rowA_original1.originalData) === null || _rowA_original_originalData1 === void 0 ? void 0 : (_rowA_original_originalData_trainer1 = _rowA_original_originalData1.trainer) === null || _rowA_original_originalData_trainer1 === void 0 ? void 0 : _rowA_original_originalData_trainer1.surname) || \"\") || \"\";\n                        const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainer = _rowB_original_originalData.trainer) === null || _rowB_original_originalData_trainer === void 0 ? void 0 : _rowB_original_originalData_trainer.firstName) || \"\", \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_originalData1 = _rowB_original1.originalData) === null || _rowB_original_originalData1 === void 0 ? void 0 : (_rowB_original_originalData_trainer1 = _rowB_original_originalData1.trainer) === null || _rowB_original_originalData_trainer1 === void 0 ? void 0 : _rowB_original_originalData_trainer1.surname) || \"\") || \"\";\n                        return valueA.localeCompare(valueB);\n                    }\n                },\n                {\n                    accessorKey: \"who\",\n                    cellAlignment: \"right\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Who\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"laptop\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_originalData_members, _training_originalData;\n                        const training = row.original;\n                        const members = ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_members = _training_originalData.members) === null || _training_originalData_members === void 0 ? void 0 : _training_originalData_members.nodes) || training.members || [];\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex items-end gap-1\",\n                            children: members.map((member, index)=>/*#__PURE__*/ {\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                size: \"sm\",\n                                                variant: \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 37\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 29\n                        }, undefined);\n                    },\n                    sortingFn: (rowA, rowB)=>{\n                        var _rowA_original_originalData_members, _rowA_original_originalData, _rowA_original, _rowA_original1, _rowB_original_originalData_members, _rowB_original_originalData, _rowB_original, _rowB_original1, _membersA_, _membersA_1, _membersB_, _membersB_1;\n                        const membersA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_members = _rowA_original_originalData.members) === null || _rowA_original_originalData_members === void 0 ? void 0 : _rowA_original_originalData_members.nodes) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.members) || [];\n                        const membersB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_members = _rowB_original_originalData.members) === null || _rowB_original_originalData_members === void 0 ? void 0 : _rowB_original_originalData_members.nodes) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.members) || [];\n                        var _membersA__firstName, _membersA__surname;\n                        const valueA = \"\".concat((_membersA__firstName = membersA === null || membersA === void 0 ? void 0 : (_membersA_ = membersA[0]) === null || _membersA_ === void 0 ? void 0 : _membersA_.firstName) !== null && _membersA__firstName !== void 0 ? _membersA__firstName : \"\", \" \").concat((_membersA__surname = membersA === null || membersA === void 0 ? void 0 : (_membersA_1 = membersA[0]) === null || _membersA_1 === void 0 ? void 0 : _membersA_1.surname) !== null && _membersA__surname !== void 0 ? _membersA__surname : \"\") || \"\";\n                        var _membersB__firstName, _membersB__surname;\n                        const valueB = \"\".concat((_membersB__firstName = membersB === null || membersB === void 0 ? void 0 : (_membersB_ = membersB[0]) === null || _membersB_ === void 0 ? void 0 : _membersB_.firstName) !== null && _membersB__firstName !== void 0 ? _membersB__firstName : \"\", \" \").concat((_membersB__surname = membersB === null || membersB === void 0 ? void 0 : (_membersB_1 = membersB[0]) === null || _membersB_1 === void 0 ? void 0 : _membersB_1.surname) !== null && _membersB__surname !== void 0 ? _membersB__surname : \"\") || \"\";\n                        return valueA.localeCompare(valueB);\n                    }\n                }\n            ];\n        }\n        // If we have only overdue/upcoming training, use overdue training columns\n        if (hasOverdueOrUpcoming && !hasCompleted) {\n            return [\n                mobileColumn,\n                {\n                    accessorKey: \"vessel\",\n                    cellAlignment: \"left\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Vessel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_vessel;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: !isVesselView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 37\n                            }, undefined)\n                        }, void 0, false);\n                    }\n                },\n                {\n                    accessorKey: \"crew\",\n                    cellAlignment: \"right\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Crew\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"laptop\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_status;\n                        const training = row.original;\n                        const members = training.members || [];\n                        return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-1\",\n                            children: members.map((member)=>/*#__PURE__*/ {\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                size: \"sm\",\n                                                variant: training.status.isOverdue ? \"destructive\" : \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, member.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 37\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 29\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"!rounded-full size-10\", (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.class),\n                            children: members.length\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                },\n                {\n                    accessorKey: \"status\",\n                    cellAlignment: \"right\",\n                    header: (param)=>{\n                        let { column } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                            column: column,\n                            title: \"Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 25\n                        }, undefined);\n                    },\n                    breakpoint: \"landscape\",\n                    cell: (param)=>{\n                        let { row } = param;\n                        var _training_status, _training_status1, _training_status2;\n                        const training = row.original;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? (_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                            children: ((_training_status2 = training.status) === null || _training_status2 === void 0 ? void 0 : _training_status2.label) || \"Unknown Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                }\n            ];\n        }\n        // Default unified view with all columns\n        return [\n            mobileColumn,\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 683,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_trainingType;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(training.status.isOverdue && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                        children: ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 25\n                    }, undefined);\n                }\n            },\n            {\n                accessorKey: \"dueDate\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-right\", training.status.isOverdue && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                        children: formatDate(training.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 25\n                    }, undefined);\n                }\n            },\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 727,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 733,\n                        columnNumber: 25\n                    }, undefined);\n                }\n            }\n        ];\n    };\n    // Create table columns\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)(getColumnsForDataType()), [\n        memberId,\n        hasOverdueOrUpcoming,\n        hasCompleted,\n        isVesselView,\n        isWide\n    ]);\n    if (!(unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: \"No training data available\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n            lineNumber: 756,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.FilteredTable, {\n        columns: columns,\n        data: unifiedData,\n        showToolbar: showToolbar,\n        rowStatus: getRowStatus,\n        pageSize: pageSize || unifiedData.length\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 763,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(UnifiedTrainingTable, \"x7VOLZGRwr6UzHBlctBEB/GNMsI=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery\n    ];\n});\n_c1 = UnifiedTrainingTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingTable);\nvar _c, _c1;\n$RefreshReg$(_c, \"UnifiedMobileTrainingCard\");\n$RefreshReg$(_c1, \"UnifiedTrainingTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/crew-training-utils */ \"(app-pages-browser)/./src/app/lib/crew-training-utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId, memberId, isVesselView = false, excludeFilters = [] } = param;\n    _s();\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter options state\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            // Extract filter options from completed training data\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            setCompletedTrainingList(data);\n            setVesselIdOptions(vesselIDs);\n            setTrainingTypeIdOptions(trainingTypeIDs);\n            setTrainerIdOptions(trainerIDs);\n            setCrewIdOptions(memberIDs);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 100,\n                limit: 100\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Use training filters hook\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_10__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: ()=>{}\n    });\n    // Memoize the load data function to prevent unnecessary re-renders\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setLoading(true);\n        await loadTrainingSessionDues(filter);\n        await loadTrainingList(page, filter);\n        setLoading(false);\n    }, [\n        filter,\n        page,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, [\n        loadData\n    ]);\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.getTrainingDataStats)(unifiedData);\n    }, [\n        unifiedData\n    ]);\n    const isLoading = loading || duesLoading || completedLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                    children: \"Crew Training Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground mt-1\",\n                                    children: \"Unified view of all training activities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"destructive\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-current rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Overdue: \",\n                                        stats.overdue\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"secondary\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Upcoming: \",\n                                        stats.upcoming\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Completed: \",\n                                        stats.completed\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    type: \"normal\",\n                                    children: [\n                                        \"Total: \",\n                                        stats.total\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            size: \"sm\",\n                            onClick: ()=>setIncludeCompleted(!includeCompleted),\n                            children: [\n                                includeCompleted ? \"Hide\" : \"Show\",\n                                \" Completed\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: loadData,\n                            disabled: isLoading,\n                            children: isLoading ? \"Loading...\" : \"Refresh\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Loading training data...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                        trainingSessionDues: trainingSessionDues,\n                        completedTrainingList: completedTrainingList,\n                        getVesselWithIcon: getVesselWithIcon,\n                        includeCompleted: includeCompleted,\n                        memberId: memberId,\n                        isVesselView: isVesselView,\n                        showToolbar: false\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 17\n                }, undefined),\n                !isLoading && unifiedData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Showing \",\n                            unifiedData.length,\n                            \" training record\",\n                            unifiedData.length !== 1 ? \"s\" : \"\",\n                            \". Data is sorted with overdue trainings first, then upcoming, then completed.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 253,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 252,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"IZHHBFmTHfJAb1oRqWDAviFOIEc=\", false, function() {\n    return [\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_10__.useTrainingFilters\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});
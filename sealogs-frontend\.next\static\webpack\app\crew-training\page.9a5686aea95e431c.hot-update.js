"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/lib/crew-training-utils.ts":
/*!********************************************!*\
  !*** ./src/app/lib/crew-training-utils.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Transforming completed training data:\", {\n        totalRecords: trainingList.length,\n        sampleRecord: trainingList[0]\n    });\n    return trainingList.map((training)=>{\n        var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n        // Enhanced vessel data transformation with position information\n        let completeVesselData = training.vessel || {\n            id: 0,\n            title: \"Unknown\"\n        };\n        if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n            try {\n                // Get complete vessel data including position, icon, and other metadata\n                completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                // Ensure we preserve original vessel data if transformation fails\n                if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                    completeVesselData = training.vessel;\n                }\n                // Add position information if available\n                if (training.vessel.position && !completeVesselData.position) {\n                    completeVesselData.position = training.vessel.position;\n                }\n                // Add location type if available\n                if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                    completeVesselData.trainingLocationType = training.trainingLocationType;\n                }\n            } catch (error) {\n                console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                completeVesselData = training.vessel;\n            }\n        }\n        // Enhanced member deduplication and normalization\n        const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n        const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n            // Check if member already exists in the accumulator\n            const existingMember = acc.find((m)=>m.id === member.id);\n            if (existingMember) {\n                // Update existing member with more complete data\n                existingMember.firstName = member.firstName || existingMember.firstName;\n                existingMember.surname = member.surname || existingMember.surname;\n                existingMember.email = member.email || existingMember.email;\n            } else {\n                // Add new member with normalized data\n                acc.push({\n                    id: member.id,\n                    firstName: member.firstName || \"\",\n                    surname: member.surname || \"\",\n                    email: member.email || \"\",\n                    ...member // Preserve any additional member data\n                });\n            }\n            return acc;\n        }, []);\n        return {\n            id: training.id,\n            dueDate: training.date,\n            vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n            vessel: completeVesselData,\n            trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n            trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                id: 0,\n                title: \"Unknown\"\n            },\n            members: deduplicatedMembers,\n            status: {\n                label: \"Completed\",\n                isOverdue: false,\n                class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                dueWithinSevenDays: false\n            },\n            category: \"completed\",\n            originalData: training\n        };\n    });\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No training session dues provided\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Raw training session dues input:\", {\n        totalRecords: trainingSessionDues.length,\n        sampleRecord: trainingSessionDues[0]\n    });\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item but log it\n            if (!hasValidVesselMembers) {\n                var _item_vessel_seaLogsMembers_nodes1, _item_vessel_seaLogsMembers1, _item_vessel1;\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Item without vessel member match:\", {\n                    memberID: item.memberID,\n                    vesselID: item.vesselID,\n                    vesselMembers: (_item_vessel1 = item.vessel) === null || _item_vessel1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers1 = _item_vessel1.seaLogsMembers) === null || _item_vessel_seaLogsMembers1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes1 = _item_vessel_seaLogsMembers1.nodes) === null || _item_vessel_seaLogsMembers_nodes1 === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes1.map((m)=>m === null || m === void 0 ? void 0 : m.id)\n                });\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n            originalCount: trainingSessionDues.length,\n            filteredCount: filteredData.length,\n            removedCount: trainingSessionDues.length - filteredData.length\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n            totalRecords: dueWithStatus.length,\n            statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n                const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n                acc[key] = (acc[key] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n            groupCount: Object.keys(groupedDues).length,\n            groupKeys: Object.keys(groupedDues)\n        });\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            var _group_status, _group_status1;\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            group.members.forEach((member)=>{\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            // Determine category based on status\n            let category;\n            if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                category = \"overdue\";\n            } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                category = \"upcoming\";\n            } else {\n                category = \"upcoming\" // Default for future due dates\n                ;\n            }\n            // Enhanced vessel data with position information\n            const enhancedVessel = group.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            // Add training location type if available\n            if (group.trainingLocationType && !enhancedVessel.trainingLocationType) {\n                enhancedVessel.trainingLocationType = group.trainingLocationType;\n            }\n            return {\n                id: group.id,\n                dueDate: group.dueDate,\n                vesselID: group.vesselID,\n                vessel: enhancedVessel,\n                trainingTypeID: group.trainingTypeID,\n                trainingType: group.trainingType || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: mergedMembers,\n                status: group.status,\n                category,\n                originalData: group\n            };\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n            totalRecords: mergedDues.length,\n            categoryBreakdown: mergedDues.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: mergedDues[0]\n        });\n        return mergedDues;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * Optimized version using Map for better performance\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for deduplication\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting deduplication:\", {\n        totalRecords: data.length,\n        sampleRecord: data[0]\n    });\n    try {\n        // Use Map for O(1) lookups instead of O(n) findIndex\n        const recordMap = new Map();\n        for (const item of data){\n            if (!item || typeof item.id !== \"number\") {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item in deduplication:\", item);\n                continue;\n            }\n            if (recordMap.has(item.id)) {\n                var // Add existing members\n                _existing_members, // Add/merge new members\n                _item_members, _item_vessel, _item_trainingType, _item_status;\n                // Merge with existing record\n                const existing = recordMap.get(item.id);\n                // Use Map for member deduplication for better performance\n                const memberMap = new Map();\n                (_existing_members = existing.members) === null || _existing_members === void 0 ? void 0 : _existing_members.forEach((member)=>{\n                    if (member && member.id) {\n                        memberMap.set(member.id, member);\n                    }\n                });\n                (_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.forEach((member)=>{\n                    if (member && member.id) {\n                        const existingMember = memberMap.get(member.id);\n                        if (existingMember) {\n                            // Merge member data, preferring non-empty values\n                            memberMap.set(member.id, {\n                                ...existingMember,\n                                firstName: member.firstName || existingMember.firstName,\n                                surname: member.surname || existingMember.surname,\n                                email: member.email || existingMember.email,\n                                ...member // Preserve any additional member data\n                            });\n                        } else {\n                            memberMap.set(member.id, member);\n                        }\n                    }\n                });\n                // Update the existing record with merged data\n                recordMap.set(item.id, {\n                    ...existing,\n                    members: Array.from(memberMap.values()),\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) && item.vessel.title !== \"Unknown\" ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) && item.trainingType.title !== \"Unknown\" ? item.trainingType : existing.trainingType,\n                    // Prefer overdue status over upcoming/completed\n                    status: ((_item_status = item.status) === null || _item_status === void 0 ? void 0 : _item_status.isOverdue) ? item.status : existing.status\n                });\n            } else {\n                // Add new record\n                recordMap.set(item.id, item);\n            }\n        }\n        const deduplicatedData = Array.from(recordMap.values());\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Deduplication complete:\", {\n            originalCount: data.length,\n            deduplicatedCount: deduplicatedData.length,\n            removedDuplicates: data.length - deduplicatedData.length,\n            categoryBreakdown: deduplicatedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        return deduplicatedData;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in deduplication:\", error);\n        return data // Return original data if deduplication fails\n        ;\n    }\n};\n/**\n * Sort unified training data with priority-based ordering\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    return data.sort((a, b)=>{\n        // First sort by priority (overdue > upcoming > completed)\n        const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n        if (priorityDiff !== 0) {\n            return priorityDiff;\n        }\n        // Within same priority, sort by date\n        const dateA = new Date(a.dueDate).getTime();\n        const dateB = new Date(b.dueDate).getTime();\n        if (a.category === \"overdue\") {\n            // For overdue: most overdue first (earliest due date first)\n            return dateA - dateB;\n        } else if (a.category === \"upcoming\") {\n            // For upcoming: soonest due date first\n            return dateA - dateB;\n        } else {\n            // For completed: most recent completion first (latest date first)\n            return dateB - dateA;\n        }\n    });\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * Optimized version with comprehensive debugging and error handling\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting merge process:\", {\n        trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n        completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n        includeCompleted,\n        debug\n    });\n    try {\n        // Transform overdue/upcoming training data\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming training session dues...\");\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed dues result:\", {\n            count: transformedDues.length,\n            sample: transformedDues[0]\n        });\n        // Transform completed training data if requested\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming completed training...\");\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed completed result:\", {\n            count: transformedCompleted.length,\n            sample: transformedCompleted[0]\n        });\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Combined data:\", {\n            totalCount: combinedData.length,\n            duesCount: transformedDues.length,\n            completedCount: transformedCompleted.length,\n            sample: combinedData[0]\n        });\n        // Remove duplicates and sort with priority-based ordering\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting deduplication...\");\n        const deduplicatedData = deduplicateTrainingData(combinedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Deduplication complete:\", {\n            beforeCount: combinedData.length,\n            afterCount: deduplicatedData.length,\n            sample: deduplicatedData[0]\n        });\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting sorting...\");\n        const sortedData = sortUnifiedTrainingData(deduplicatedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Final result:\", {\n            totalCount: sortedData.length,\n            categoryBreakdown: sortedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sample: sortedData[0]\n        });\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        console.error(\"❌ Error stack:\", error instanceof Error ? error.stack : \"No stack trace\");\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/crew-training-utils.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/crew-training-utils */ \"(app-pages-browser)/./src/app/lib/crew-training-utils.ts\");\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId = 0, memberId = 0, isVesselView = false, excludeFilters = [\n        \"overdueToggle\"\n    ] } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_14__.useRouter)();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [includeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true) // Always include completed in unified view\n    ;\n    // Filter options state\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.getPermissions);\n    }, []);\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            // Extract filter options from completed training data\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            setCompletedTrainingList(data);\n            setVesselIdOptions(vesselIDs);\n            setTrainingTypeIdOptions(trainingTypeIDs);\n            setTrainerIdOptions(trainerIDs);\n            setCrewIdOptions(memberIDs);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 20,\n                limit: 20\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Use training filters hook\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_11__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: ()=>{}\n    });\n    // Memoize the load data function to prevent unnecessary re-renders\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        await loadTrainingSessionDues(filter);\n        await loadTrainingList(page, filter);\n        setIsLoading(false);\n    }, [\n        filter,\n        page,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const f = {\n            ...filter\n        };\n        if (memberId && +memberId > 0) {\n            f.members = {\n                id: {\n                    contains: +memberId\n                }\n            };\n        }\n        setFilter(f);\n        loadData();\n    }, []) // Only run on mount\n    ;\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.getTrainingDataStats)(unifiedData);\n    }, [\n        unifiedData\n    ]);\n    // Enhanced loading state management\n    const combinedLoading = isLoading || duesLoading || completedLoading;\n    const hasOverdueUpcomingData = trainingSessionDues && trainingSessionDues.length > 0;\n    const hasCompletedData = completedTrainingList && completedTrainingList.length > 0;\n    // Create detailed loading status\n    const getLoadingStatus = ()=>{\n        const statuses = [];\n        if (duesLoading) statuses.push(\"overdue/upcoming training\");\n        if (completedLoading) statuses.push(\"completed training\");\n        if (isLoading) statuses.push(\"training data\");\n        return statuses;\n    };\n    // Comprehensive loading component\n    const LoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Loading training data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 21\n                            }, undefined),\n                            status.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"Currently loading: \",\n                                    status.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 291,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 290,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Partial loading component for when some data is available\n    const PartialLoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-4 bg-muted/30 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Loading \",\n                            status.join(\", \"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 310,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 309,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Check permissions\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_12__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 326,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 328,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_10__.TrainingListFilter, {\n                    memberId: memberId,\n                    onChange: handleFilterChange,\n                    vesselIdOptions: vesselIdOptions,\n                    trainingTypeIdOptions: trainingTypeIdOptions,\n                    trainerIdOptions: trainerIdOptions,\n                    memberIdOptions: crewIdOptions,\n                    excludeFilters: excludeFilters\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 335,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                            children: \"Crew Training Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mt-1\",\n                                            children: \"Unified view of all training activities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"destructive\",\n                                            type: \"normal\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-current rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"Overdue: \",\n                                                stats.overdue\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"secondary\",\n                                            type: \"normal\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"Upcoming: \",\n                                                stats.upcoming\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"outline\",\n                                            type: \"normal\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"Completed: \",\n                                                stats.completed\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"outline\",\n                                            type: \"normal\",\n                                            children: [\n                                                \"Total: \",\n                                                stats.total\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    size: \"sm\",\n                                    onClick: ()=>setIncludeCompleted(!includeCompleted),\n                                    children: [\n                                        includeCompleted ? \"Hide\" : \"Show\",\n                                        \" Completed\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: loadData,\n                                    disabled: isLoading,\n                                    children: isLoading ? \"Loading...\" : \"Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: isLoading && !hasOverdueUpcomingData && !hasCompletedData ? // Full loading state when no data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingIndicator, {\n                                status: getLoadingStatus()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 29\n                            }, undefined) : isLoading && (hasOverdueUpcomingData || hasCompletedData) ? // Partial loading state when some data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                        trainingSessionDues: trainingSessionDues,\n                                        completedTrainingList: completedTrainingList,\n                                        getVesselWithIcon: getVesselWithIcon,\n                                        includeCompleted: includeCompleted,\n                                        memberId: memberId,\n                                        isVesselView: isVesselView,\n                                        showToolbar: false,\n                                        pageSize: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PartialLoadingIndicator, {\n                                        status: getLoadingStatus()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 29\n                            }, undefined) : // Normal state with data\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                trainingSessionDues: trainingSessionDues,\n                                completedTrainingList: completedTrainingList,\n                                getVesselWithIcon: getVesselWithIcon,\n                                includeCompleted: includeCompleted,\n                                memberId: memberId,\n                                isVesselView: isVesselView,\n                                showToolbar: false,\n                                pageSize: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 21\n                        }, undefined),\n                        !isLoading && unifiedData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"Showing \",\n                                        unifiedData.length,\n                                        \" training record\",\n                                        unifiedData.length !== 1 ? \"s\" : \"\",\n                                        \". Data is sorted with overdue trainings first, then upcoming, then completed.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-4 text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Overdue/Upcoming:\",\n                                                \" \",\n                                                (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Completed:\",\n                                                \" \",\n                                                (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last updated:\",\n                                                \" \",\n                                                new Date().toLocaleTimeString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 25\n                        }, undefined),\n                        isLoading && (hasOverdueUpcomingData || hasCompletedData) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Some data is still loading. The table will update automatically when new data becomes available.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 29\n                        }, undefined),\n                        !isLoading && unifiedData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"No training data available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs mt-1\",\n                                    children: \"Try adjusting your filters or refresh the data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 347,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 333,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"IXgvIws1t6KACqgzoEsW5me2iJQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_14__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_11__.useTrainingFilters\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});